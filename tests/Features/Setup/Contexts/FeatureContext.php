<?php

namespace Features\Setup\Contexts;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\GuestConsumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Cloud\Aws\Adapters\AwsCredentialsAdapter;
use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Modules\Accounts\Contracts\GlobalAccountRepository;
use AwardForce\Modules\Authentication\Data\GlobalAuthenticator;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Features\Data\FeatureRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Holocron\Services\FeatureIntros;
use AwardForce\Modules\Identity\Roles\Contracts\PermissionRepository;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Roles\Services\RoleSeedingService;
use AwardForce\Modules\Identity\Roles\ValueObjects\Mode;
use AwardForce\Modules\Identity\Users\Contracts\GlobalCommunicationChannelRepository;
use AwardForce\Modules\Identity\Users\Contracts\GlobalUserRepository;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\GlobalUser;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Repositories\EloquentGlobalCommunicationChannelRepository;
use AwardForce\Modules\Identity\Users\Repositories\EloquentUserProvider;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\Regions\Data\Region;
use AwardForce\Modules\Regions\Data\RegionRepository;
use AwardForce\Modules\Rounds\Models\Round;
use Behat\Behat\Context\Context;
use Behat\Behat\Context\SnippetAcceptingContext;
use Behat\MinkExtension\Context\MinkContext;
use Carbon\Carbon;
use DB;
use Features\Setup\Assertions;
use Features\Setup\GlobalSessionGuard;
use Google\Cloud\Core\Timestamp;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Session;
use Laracasts\Behat\Context\KernelAwareContext;
use Laracasts\Behat\Context\Migrator;
use Mockery as m;
use PHPUnit\Framework\Assert as PHPUnit;
use Platform\Database\Eloquent\Repository;
use Platform\Features\Feature;
use Platform\Features\Features;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Factory\MuffinFactory;
use Tests\Factory\SpeedBurst;
use Tests\Provisioning\ArrayGlobalAccountRepository;
use Tests\Provisioning\ArrayGlobalUserRepository;

/**
 * Defines application features from the specific context.
 */
#[\AllowDynamicProperties]
class FeatureContext extends MinkContext implements Context, KernelAwareContext, SnippetAcceptingContext
{
    use AccountFeatures;
    use AllocationPaymentFeatures;
    use ApiKeysFeatures;
    use Assertions;
    use AssetsFeatures;
    use AssignmentFeatures;
    use AuditFeatures;
    use Authentication;
    use AwardFeatures;
    use BillingFeatures;
    use BroadcastFeatures;
    use CategoryFeatures;
    use ChapterFeatures;
    use ClearFeatures;
    use CollaborationFeature;
    use ContentBlockFeatures;
    use ContractFeatures;
    use DashboardFeatures;
    use DocumentsFeatures;
    use DocumentTemplatesFeatures;
    use DownloadFeatures;
    use EcommerceFeatures;
    use EntryFeatures;
    use ExportFeatures;
    use FieldFeatures;
    use FileFeatures;
    use FormsFeatures;
    use FundingFeatures;
    use GrantFeatures;
    use GrantReportFeatures;
    use HomepageFeatures;
    use IntegrationFeatures;
    use JudgeFeatures;
    use LeaderboardFeatures;
    use MarketplaceFeatures;
    use Migrator;

    //    use InteractsWithPages {
    //        InteractsWithPages::followRedirects as followNormalRedirects;
    //    }
    use MuffinFactory;
    use NewDashboardFeatures;
    use NotificationFeatures;
    use PanelFeatures;
    use PaymentMethodsFeatures;
    use PaymentSettingsFeatures;
    use PaymentSubscriptionsFeatures;
    use PriceFeatures;
    use ProfileFeature;
    use ProgressFeatures;
    use ProvisioningFeatures;
    use RefereeFeatures;
    use ReportFeatures;
    use ReviewFlowFeatures;
    use RoleFeatures;
    use RoundFeatures;
    use ScoreSetFeatures;
    use ScoringCriterionFeatures;
    use SeasonFeatures;
    use SettingFeatures;
    use SpeedBurst;
    use Subscription;
    use TabFeatures;
    use TagFeatures;
    use TaxFeatures;
    use TermsFeatures;
    use ThemeFeatures;
    use UserFeatures;
    use WebhooksFeatures;
    use ZapierFeatures;

    protected $entry;
    protected $consumer;
    protected $field;
    protected $formSlug;
    protected $stepOneField;
    protected $stepTwoField;
    protected $entries = [];
    protected $allocations = [];
    protected $rounds = [];
    protected $webhooks = [];
    protected $documents = [];
    protected $document;
    protected $documentTemplates = [];
    protected $documentTemplate;

    /**
     * @var Round
     */
    protected $round;

    /**
     * @var Chapter
     */
    protected $chapter;

    /**
     * @var Category
     */
    protected $category;

    /**
     * @var Panel
     */
    protected $panel;

    /**
     * @var Notification
     */
    protected $notification;

    private ?User $existingUser = null;
    protected Form $reportForm;
    private $draftSeason;

    public function __construct()
    {
        $this->mockHasher();

        DB::beginTransaction();

        $this->prepareIdentityDatabase();

        $this->prepareMuffins();
        //        $this->setupUserWithRole('Entrant');
    }

    /**
     * @AfterScenario
     */
    public function after()
    {
        app(FeatureRepository::class)->nukeRequestCache();

        DB::rollback();
        DB::disconnect();
    }

    /**
     * Set the kernel instance on the context.
     *
     * @return mixed
     */
    public function setApp(HttpKernelInterface $kernel): void
    {
        $this->app = $kernel;

        app()->instance(GlobalAccountRepository::class, new ArrayGlobalAccountRepository);
        app()->instance(GlobalUserRepository::class, new ArrayGlobalUserRepository);
        app()->instance(RegionRepository::class, new class extends Repository implements RegionRepository
        {
            protected function getQuery()
            {
            }

            public function getOneBy($field, $value)
            {
                return new Region(['code' => current_account()->region]);
            }
        });

        $featureIntrosMock = m::mock(FeatureIntros::class);
        $featureIntrosMock->shouldReceive('forRoute')->andReturn(null);
        app()->instance(FeatureIntros::class, $featureIntrosMock);

        $stsAdapter = m::mock(AwsCredentialsAdapter::class, [$this->app['config']]);
        $stsAdapter->shouldReceive('key')->andReturn('temp_key');
        $stsAdapter->shouldReceive('secret')->andReturn('temp_secret');
        $stsAdapter->shouldReceive('token')->andReturn('temp_token');

        app()->instance(AwsCredentialsAdapter::class, $stsAdapter);
        $database = m::mock(Database::class);
        $database->shouldReceive('expireAt')->andReturn(new Timestamp(new \DateTime()));
        app()->instance(Database::class, $database);
    }

    /**
     * @Given I am not currently logged in
     */
    public function iAmNotCurrentlyLoggedIn()
    {
        Session::flush();
    }

    /**
     * @Given I have a valid account with :email and :password
     */
    public function iHaveAValidAccountWith($email, $password)
    {
        $this->user = $this->muffin(User::class, ['email' => $email]);
        $this->user->globalUser->password = Hash::make($password);
    }

    /**
     * @Given I try to access the login page with a redirect to :route
     */
    public function iTryToAccessTheLoginPageWithARedirectTo($route)
    {
        $this->call('GET', route('clear.login').'?redirect='.route($route));
    }

    /**
     * @Given I try to view the list of applications
     */
    public function iTryToViewTheListOfApplications()
    {
        $this->route('GET', 'entry.entrant.index');
    }

    /**
     * @Then I should see the 401 page
     */
    public function iShouldSeeThe401Page()
    {
        $this->assertResponseStatus(401);
    }

    /**
     * @Given I am associated with the current account
     */
    public function iAmAssociatedWithTheCurrentAccount()
    {
        $this->user->addToAccount(current_account());
    }

    /**
     * @Given I have a valid :role account
     */
    public function iHaveAValidAccount($role)
    {
        $this->setupUserWithRole($role, true);
    }

    /**
     * @Given there is a required :resource field
     */
    public function thereIsARequiredField($resource)
    {
        $this->field = $this->muffin(Field::class, [
            'resource' => $resource,
            'required' => true,
        ]);
    }

    /**
     * @Given there is an optional :resource field
     */
    public function thereIsAnOptionalField($resource)
    {
        $this->field = $this->muffin(Field::class, [
            'resource' => $resource,
            'required' => false,
        ]);
    }

    /**
     * @When I login to my account
     */
    public function iLoginToMyAccount()
    {
        $globalUsers = m::mock(GlobalUserRepository::class);
        app()->instance(GlobalUserRepository::class, $globalUsers);
        $globalUsers->shouldReceive('getByEmailOrMobile')->andReturn($this->user->globalUser);

        $users = m::mock(UserRepository::class);
        app()->instance(UserRepository::class, $users);
        $users->shouldReceive('getByEmailOrMobile')->andReturn($this->user);
        $users->shouldReceive('countAccounts')->andReturn(1);

        $this->action('POST', 'AuthenticationController@login', [
            'login' => $this->user->email,
            'login_password' => $this->userPassword,
        ]);

        PHPUnit::assertTrue(Auth::check());

        if ($this->response->isRedirect()) {
            $this->call('GET', $this->response->getTargetUrl());
        }
    }

    /**
     * @Then I should be prompted to fill in account fields
     * @Then I should be logged in
     */
    public function iShouldBePromptedToFillInAccountFields()
    {
        $this->assertRedirectedToRoute('profile.complete');
        $this->route('GET', 'profile.complete');
    }

    /**
     * @Then I should be able to log in to the application with :login and :password
     */
    public function iShouldBeAbleToLogInToTheApplication($login, $password)
    {
        $globalUser = new GlobalUser();
        $globalUser->id = Uuid::uuid4();
        $globalUser->email = hash('sha256', $login);
        $globalUser->password = Hash::make($password);

        $globalUsers = \Mockery::mock(GlobalUserRepository::class);
        app()->instance(GlobalUserRepository::class, $globalUsers);

        $globalUsers->shouldReceive('getByEmailOrMobile')->andReturn($globalUser);

        $this->action('POST', 'AuthenticationController@login', ['login' => $login, 'login_password' => $password]);

        $this->assertRedirectedToRoute('profile.show');
    }

    /**
     * @Then I should receive a reminder email
     */
    public function iShouldBeAbleToResetMyPassword()
    {
        $users = $this->createUserAccounts(1);

        $this->action('POST', 'AuthenticationController@passwordReminder', $this->withInput(['forgotLogin' => $users[0]->email]));
        $this->assertRedirectedToRoute('home');
    }

    /**
     * @Then I should be able to log out of the application
     */
    public function iShouldBeAbleToLogOutOfTheApplication()
    {
        return $this->action('POST', 'AuthenticationController@logout');
    }

    /**
     * @Given I am currently logged in
     */
    public function iAmCurrentlyLoggedIn()
    {
        $users = $this->createUserAccounts(1);
        $this->user = $users[0];
        $this->be($this->user);

        PHPUnit::assertTrue(Auth::check());
    }

    /**
     * @Then I should not be able to log in to the application with :login and :password
     */
    public function iShouldNotBeAbleToLogInToTheApplicationWithAnd($login, $password)
    {
        $this->action('POST', 'AuthenticationController@login', compact('login', 'password'));

        $this->assertRedirectedToRoute('home');

        PHPUnit::assertTrue(Auth::guest());
    }

    /**
     * @Given I am currently logged in as an :role in :timezone
     */
    public function iAmCurrentlyLoggedInAsRoleInTimezone($role, $timezone)
    {
        $this->setupUserWithRole($role, true, timezone: $timezone);
        $this->setConsumer();
    }

    /**
     * @Given I am currently logged in as an :role
     */
    public function iAmCurrentlyLoggedInAsAn($role)
    {
        Auth::logout();
        Session::invalidate();
        $this->setupUserWithRole($role, true);
        $this->setConsumer();
        $this->setGlobalCommunicationChannels();
    }

    protected function setConsumer()
    {
        $this->consumer = new UserConsumer($this->user, app(PermissionRepository::class));
        Consumer::set($this->consumer);
    }

    /**
     * @Given /^my role is allowed on registration$/
     */
    public function myRoleIsAllowedOnRegistration()
    {
        $role = $this->user->roles()->first();
        $role->registration = 1;
        $role->save();
    }

    /**
     * @Given I have :role permissions
     */
    public function iHavePermissions($role)
    {
        $method = 'setup'.str_replace(' ', '', $role);
        $roleSeeder = app(RoleSeedingService::class);

        $role = $roleSeeder->$method(current_account());

        app(UserRepository::class)->assignToRole($this->user, $role->id);
    }

    /**
     * @Given there is a default :role role
     */
    public function thereIsADefaultRole($role)
    {
        $method = 'setup'.str_replace(' ', '', $role);
        $roleSeeder = app(RoleSeedingService::class);

        $this->role = $roleSeeder->$method(current_account());
    }

    /**
     * @Given I can view all scores
     */
    public function iCanViewAllScores()
    {
        $this->role->permissions()->save(Permission::add($this->role, 'ScoresAll', 'view', new Mode('allow')));
    }

    /**
     * Creates a number of user accounts and assigns them.
     *
     * @return array
     */
    protected function createUserAccounts($howMany)
    {
        $users = [];

        for ($i = 0; $i < $howMany; $i++) {
            $user = $this->muffin(User::class);
            $user->addToAccount(current_account());

            $users[] = $user;
        }

        return $users;
    }

    /**
     * @Given Add user memberships
     */
    public function assignUserToMultipleAccountsD()
    {
        $this->user->globalUser->memberships = array_merge($this->user->globalUser->memberships, [Uuid::uuid4()->toString()]);
    }

    /**
     * This is a helper method to ensure that every request we make via behat that requires input,
     * also provides the necessary CSRF token that is validated on every request.
     *
     * @return array
     */
    protected function withInput(array $input)
    {
        Session::start();

        return array_merge(['_token' => csrf_token()], $input);
    }

    /**
     * @Then /^I should be able to see the export option$/
     */
    public function iShouldBeAbleToSeeTheExportOption()
    {
        $this->assertResponseContains('Export');
        $this->assertResponseContains('Excel');
        $this->assertResponseContains('CSV');
    }

    /**
     * @Given /^the ([^"]*) feature is ([^"]*)$/
     */
    public function theFeatureIsEnabled(string $feature, string $status)
    {
        current_account()->defineFeatures(new Features([new Feature($feature, $status)]));

        app(FeatureRepository::class)->nukeRequestCache();
    }

    /**
     * @Then /^the response should have "(?P<text>(?:[^"]|\\")*)"$/
     */
    public function assertResponseShouldHave($text)
    {
        $this->assertResponseContains($text);
    }

    /**
     * @Given /^(\d+) minutes have passed$/
     */
    public function minutesHavePassed(int $minutes)
    {
        Carbon::setTestNow(now()->addMinutes($minutes));
    }

    /**
     * @Given /^Setup as Guest user$/
     */
    public function setupAsGuestUser()
    {
        \Consumer::set(new GuestConsumer(null));
    }

    private function setGlobalCommunicationChannels()
    {
        $globalCommunicationChannels = m::mock(GlobalCommunicationChannelRepository::class);
        $globalCommunicationChannels->shouldReceive('awaitingConfirmation')->twice();
        $globalCommunicationChannels->shouldReceive('hasConfirmedChannel')->twice();
        app()->instance(GlobalCommunicationChannelRepository::class, $globalCommunicationChannels);
    }

    /**
     * @Given /^globals are mocked$/
     */
    public function globalsAreMocked()
    {
        app()->instance(GlobalCommunicationChannelRepository::class, $globalCommunications = m::mock(EloquentGlobalCommunicationChannelRepository::class));
        $userProvider = m::spy(EloquentUserProvider::class);
        app(AuthManager::class)->provider('eloquent', fn() => $userProvider);

        $globalCommunications->shouldReceive('getByUserAndChannel')->andReturnNull();

        $userProvider->shouldReceive('retrieveById')->andReturnUsing(function ($id) {
            $user = (app(UserRepository::class))->getById($id);
            if ($user) {
                $globalUser = new GlobalUser();
                $globalUser->id = $user->globalUserId ?: Uuid::uuid4();
                $globalUser->email = hash('sha256', $user->email);
                $globalUser->password = $user->password;
                $globalUser->setRelation('authenticators', collect([new GlobalAuthenticator()]));
                $user->setRelation('globalUser', $globalUser);
            }

            return $user;
        });

        app()->instance(Guard::class, new GlobalSessionGuard(
            'web',
            $userProvider,
            $this->app['session.store'],
        ));
    }

    /**
     * @Then  /^I should be redirected to feature ([^"]*) disabled page$/
     */
    public function iShouldBeRedirectedToFeatureDisabledPage(string $feature)
    {
        $this->assertRedirectedUriContains("feature-disabled/$feature");
    }

    /**
     * @Given I tried to enter :route and receive status code :code
     */
    public function iTriedToEnterRouteAndReceiveStatusCode(string $route, int $code)
    {
        $this->route('GET', $route);
        $this->assertResponseStatus($code);
    }

    /**
     * @Then I should be redirected to :feature disabled
     */
    public function iShouldBeRedirectedToFeatureDisabled($feature)
    {
        $this->assertRedirectedToRoute('feature.disabled', ['feature' => $feature]);
    }

    /**
     * @Then The response should be successfully
     */
    public function theResponseShouldBeSuccessfully()
    {
        $this->assertResponseOk();
    }

    /**
     * @Then save view form should not be on the page
     */
    public function saveViewFormShouldNotBeOnThePage()
    {
        $this->assertResponseNotContains('<save-view-form');
        $this->assertResponseNotContains('saved-views-list');
    }

    /**
     * @Then save view form should be on the page
     */
    public function saveViewFormShouldBeOnThePage()
    {
        $this->assertResponseContains('<save-view-form');
        $this->assertResponseContains('saved-views-list');
    }

    private function prepareIdentityDatabase()
    {
        try {
            Schema::connection('identity')->create('global_accounts', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->uuid('owner_id')->index();
                $table->string('local_slug', 8)->index();
                $table->json('translations');
                $table->json('domains');
                $table->json('supported_languages');
                $table->string('default_language');
                $table->json('api_keys')->nullable();
                $table->boolean('suspended')->default(false);
                $table->string('region');
                $table->string('product');
                $table->string('plan');
                $table->string('three_letter_code')->nullable();
                $table->string('database');
                $table->string('deal_id')->nullable();
                $table->date('start_date')->nullable();
                $table->timestamps();
            });

            Schema::connection('identity')->create('global_users', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->char('email', 64)->nullable();
                $table->char('mobile', 64)->nullable();
                $table->string('password', 64)->nullable();
                $table->boolean('require_authenticator')->default(false);
                $table->json('memberships')->nullable();
                $table->string('language')->nullable();
                $table->timestamps();
            });
        } catch (\Exception $e) {
        }
    }

    /**
     * @Then I should receive unauthorised error
     */
    public function iShouldReceiveUnauthorisedError(string $message = 'This action is unauthorized.'): void
    {
        $this->assertTrue($this->response?->exception?->getMessage() === $message);
    }

    /**
     * @Given the permission resource :resource for action :action has mode set to :mode
     * ITS J.1
     */
    public function permissionResourceForActionHasMode($resource, $action, $mode)
    {
        $roleId = \Consumer::user()->roles->first()->id;
        $permission = app(PermissionRepository::class)
            ->getByRole($roleId, $resource, $action)
            ->first();

        if (empty($permission)) {
            $permission = new Permission([
                'resource' => $resource,
                'action' => $action,
                'mode' => new Mode($mode),
            ]);
            $permission->roleId = $roleId;
        } else {
            $permission->mode = new Mode($mode);
        }

        app(PermissionRepository::class)->save($permission);
    }
}
