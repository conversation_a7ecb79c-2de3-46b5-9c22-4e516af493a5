<?php

namespace Tests\Modules\Comments\Models;

use AwardForce\Modules\Comments\Models\Comment;
use AwardForce\Modules\Comments\Tags\StringTag;
use AwardForce\Modules\Identity\Users\Models\User;
use Tests\Modules\Assignments\Services\TestHelpers;
use Tests\UnitTestCase;

final class CommentTest extends UnitTestCase
{
    use TestHelpers;

    public function testTagsArray(): void
    {
        $comment = new Comment;
        $comment->tags = 'single_1 multiple_2 multiple_3 string';

        $tags = $comment->tagsArray();

        $this->assertCount(3, $tags);
        $this->assertArrayHasKey('single', $tags);
        $this->assertEquals([1], $tags['single']);

        $this->assertArrayHasKey('multiple', $tags);
        $this->assertCount(2, $tags['multiple']);

        $this->assertContains('2', $tags['multiple']);
        $this->assertContains('3', $tags['multiple']);

        $this->assertContains('string', $tags);
    }

    public function testIsSystemGenerated(): void
    {
        $comment = new Comment;
        $comment->userId = 0;

        $this->assertTrue($comment->isSystemGenerated());
        $this->assertFalse($comment->isUserGenerated());
    }

    public function testIsUserGenerated(): void
    {
        $comment = new Comment;
        $comment->userId = 1;

        $this->assertTrue($comment->isUserGenerated());
        $this->assertFalse($comment->isSystemGenerated());
    }

    public function testGetName(): void
    {
        $this->mockTranslations();
        $this->translator->shouldReceive('get')->andReturn('comments.system.name');

        $commentOne = new Comment;
        $commentOne->userId = 0;

        $commentTwo = new Comment;
        $commentTwo->userId = 1;
        $commentTwo->setRelation('user', new User(['firstName' => 'John', 'lastName' => 'Doe']));

        $this->assertEquals('comments.system.name', $commentOne->name());
        $this->assertEquals('John Doe', $commentTwo->name());
    }

    public function testHasTag(): void
    {
        $comment = new Comment(['tags' => 'order_1']);

        $this->assertTrue($comment->hasTag('order_1'));
        $this->assertFalse($comment->hasTag('order_2'));
    }

    public function testTagsFlatArray(): void
    {
        $comment = new Comment(['tags' => 'order_1 order_2']);

        $this->assertEquals(['order_1', 'order_2'], $comment->tagsFlatArray());
    }

    public function testGetResourceId(): void
    {
        $comment = new Comment(['tags' => 'entry_2 scoreset_4 judging']);

        $this->assertEquals('2', $comment->getResourceId('entry'));
        $this->assertEquals('4', $comment->getResourceId('scoreset'));

        $this->assertNull($comment->getResourceId('order'));
    }

    public function testIsAuthor(): void
    {
        Comment::unguard();

        $comment = new Comment(['user_id' => 3]);

        $this->assertFalse($comment->isAuthor(1));
        $this->assertTrue($comment->isAuthor(3));
    }

    public function testHtmlReturnsHtmlForMarkdown(): void
    {
        Comment::unguard();

        $comment = new Comment(['user_id' => 3, 'comment' => $text = 'Text **in bold**']);

        $this->assertEquals($text, $comment->comment);
        $this->assertEquals('Text <strong>in bold</strong>', $comment->html);
    }

    public function testHtmlReturnsHtmlForNonMarkdown_simplemde()
    {
        $this->setCkEditorFeature(false);

        Comment::unguard();

        $commentA = new Comment(['user_id' => 3, 'comment' => $text = 'Text in **bold**']);

        $this->assertEquals($text, $commentA->comment);
        $this->assertEquals('Text in <strong>bold</strong>', $commentA->html);

        $commentB = new Comment(['user_id' => 3, 'comment' => $text = 'Text in html <b>bold</b>']);

        $this->assertEquals($text, $commentB->comment);
        $this->assertEquals('Text in html <b>bold</b>', $commentB->html);
    }

    public function testHtmlReturnsHtmlForNonMarkdown_ckeditor()
    {
        $this->setCkEditorFeature(true);

        Comment::unguard();

        $commentA = new Comment(['user_id' => 3, 'comment' => $text = '<p>Text in **markdown bold**</p>']);

        $this->assertEquals($text, $commentA->comment);
        $this->assertEquals('<p>Text in **markdown bold**</p>', $commentA->html);

        $commentB = new Comment(['user_id' => 3, 'comment' => $text = '<p>Text in html <b>bold</b></p>']);

        $this->assertEquals($text, $commentB->comment);
        $this->assertEquals('<p>Text in html <b>bold</b></p>', $commentB->html);
    }

    public function testIsApiMethod()
    {
        $comment = new Comment(['tags' => new StringTag('api')]);

        $this->assertTrue($comment->fromApi());
    }

    public function testItSanitisesComment(): void
    {
        $comment = new Comment;

        $comment->comment = '<script>alert("XSS")</script>';
        $html = \Blade::render('{!! $comment !!}', ['comment' => $comment->comment]);
        $this->assertEquals('', $html);

        $comment->comment = '{{toString().constructor.constructor(\'alert(\"Content XSS\")\')()}}';
        $html = \Blade::render('{!! $comment !!}', ['comment' => $comment->comment]);
        $this->assertEquals('{ {toString().constructor.constructor(&#039;alert(\&#34;Content XSS\&#34;)&#039;)()} }', $html);

        $comment->comment = '&lt;script&gt;alert(&quot;XSS&quot;)&lt;/script&gt;';
        $html = \Blade::render('{!! $comment !!}', ['comment' => $comment->comment]);
        $this->assertEquals('&lt;script&gt;alert(&#34;XSS&#34;)&lt;/script&gt;', $html);
    }
}
