<?php

namespace Tests\Library\Composers\Menu;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\GuestConsumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Composers\MainMenuComposer;
use AwardForce\Library\Html\BreadcrumbResolver;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Assignments\Services\SyncFilter;
use AwardForce\Modules\Assignments\Services\Synchroniser;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Galleries\Services\ContextGalleries;
use AwardForce\Modules\Identity\Roles\Contracts\PermissionRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Roles\Models\PermissionCollection;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Roles\Models\Roles;
use AwardForce\Modules\Identity\Roles\Services\RoleSeedingService;
use AwardForce\Modules\Identity\Roles\ValueObjects\Mode;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Judging\Services\Dashboard\ContextJudging;
use AwardForce\Modules\Menu\Services\ContextService;
use AwardForce\Modules\Menu\Services\Generators\MainMenuGenerator;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTaskRepository;
use AwardForce\Modules\ReviewFlow\Services\CurrentReviewTasks;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Settings\Models\Setting;
use Illuminate\Contracts\View\View;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Platform\Features\Feature;
use Platform\Features\Features;
use Tests\IntegratedTestCase;
use Tests\Modules\Assignments\Services\TestHelpers;

final class MainMenuComposerTest extends IntegratedTestCase
{
    use TestHelpers;

    private View $view;
    private ContextService $contextService;
    private string $menuCachekey;

    public function init()
    {
        $this->view = m::mock(View::class);
        $this->view->shouldReceive('with');
        $this->contextService = app(ContextService::class);

        $fakeUser = $this->muffin(User::class);
        \Auth::shouldReceive('user')->andReturn($fakeUser);
        $this->menuCachekey = current_account()->getId().'.'.$fakeUser->id.'.generated.menu.key';
    }

    public function testProgramManagerContextContextMenuItems(): void
    {
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);

        $contexts = json_decode($this->view->contexts, true);
        $arrayColumn = array_column($contexts, 'name');

        $this->assertNotFalse(array_search('manage', $arrayColumn));
        $this->assertNotFalse(array_search('enter', $arrayColumn));
        //        $this->assertNotFalse(array_search('guest', $arrayColumn));
        $this->assertFalse(array_search('judge', $arrayColumn));
    }

    public function testChapterManagerContextContextMenuItems(): void
    {
        $user = $this->setupUserWithRole('Chapter manager');
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $contexts = json_decode($this->view->contexts, true);
        $arrayColumn = array_column($contexts, 'name');

        $this->assertNotFalse(array_search('manage', $arrayColumn));
        $this->assertFalse(array_search('enter', $arrayColumn));
        //        $this->assertNotFalse(array_search('guest', $arrayColumn));
        $this->assertFalse(array_search('judge', $arrayColumn));
    }

    public function testEntrantContextMenuItems(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $contexts = json_decode($this->view->contexts, true);
        $arrayColumn = array_column($contexts, 'name');

        $this->assertFalse(array_search('manage', $arrayColumn));
        $this->assertNotFalse(array_search('enter', $arrayColumn));
        $this->assertFalse(array_search('guest', $arrayColumn));
        $this->assertFalse(array_search('judge', $arrayColumn));
    }

    public function testJudgeContextMenuItems(): void
    {
        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $contexts = json_decode($this->view->contexts, true);

        $arrayColumn = array_column($contexts, 'name');

        $this->assertFalse(array_search('manage', $arrayColumn));
        $this->assertFalse(array_search('enter', $arrayColumn));
        $this->assertFalse(array_search('guest', $arrayColumn));
        $this->assertNotFalse(array_search('judge', $arrayColumn));
    }

    public function testEntrantJudgeContextMenuItems(): void
    {
        $user = $this->setupUserWithRole('Judge');
        $entrantRole = app(RoleSeedingService::class)->setupEntrant(current_account());
        $user->roles()->attach($entrantRole->id);
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $contexts = json_decode($this->view->contexts, true);

        $arrayColumn = array_column($contexts, 'name');

        $this->assertFalse(array_search('manage', $arrayColumn));
        $this->assertNotFalse(array_search('enter', $arrayColumn));
        $this->assertFalse(array_search('guest', $arrayColumn));
        $this->assertNotFalse(array_search('judge', $arrayColumn));
    }

    public function testProgramManagerJudgeContextMenuItems(): void
    {
        $user = $this->setupUserWithRole('Program manager');
        $judgeRole = app(RoleSeedingService::class)->setupJudge(current_account());
        $user->roles()->attach($judgeRole->id);
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $contexts = json_decode($this->view->contexts, true);

        $arrayColumn = array_column($contexts, 'name');

        $this->assertNotFalse(array_search('manage', $arrayColumn));
        $this->assertNotFalse(array_search('enter', $arrayColumn));
        //        $this->assertNotFalse(array_search('guest', $arrayColumn));
        $this->assertNotFalse(array_search('judge', $arrayColumn));
    }

    public function testChapterManagerJudgeContextMenuItems(): void
    {
        $user = $this->setupUserWithRole('Chapter manager');
        $entrantRole = app(RoleSeedingService::class)->setupJudge(current_account());
        $user->roles()->attach($entrantRole->id);
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $contexts = json_decode($this->view->contexts, true);

        $arrayColumn = array_column($contexts, 'name');

        $this->assertNotFalse(array_search('manage', $arrayColumn));
        $this->assertFalse(array_search('enter', $arrayColumn));
        //        $this->assertNotFalse(array_search('guest', $arrayColumn));
        $this->assertNotFalse(array_search('judge', $arrayColumn));
    }

    public function testProgramManagerMenu(): void
    {
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        app()->instance(CurrentReviewTasks::class, $currentReviewTasks = m::mock(CurrentReviewTasks::class));
        $currentReviewTasks->shouldReceive('hasActiveReviewTasks')->andReturn(true);
        $currentReviewTasks->shouldReceive('pendingTasks')->andReturn(0);

        $menuItemNames = $this->generateUserMenuByContext();

        $expectedManageLinks = ['dashboard', 'guides-and-tours', 'manage-entries', 'review-flow-settings', 'manage-judging',
            'leaderboard', 'judging-progress', 'users', 'settings-account',
            'settings-custom-export-layout', 'settings-languages', 'settings-notifications', 'settings-review-flow',
            'settings-seasons', 'settings-notifications', 'settings-languages', 'content-block-settings',
            'interface-text-settings', 'terms-settings', 'general-categories', 'chapter-settings',
            'field-settings', 'rounds-settings', 'tabs-settings', 'assignment-settings', 'awards-settings',
            'panels-settings', 'rounds-settings', 'scoresets-settings', 'rounds-settings', 'broadcasts-settings',
            'notifications-settings', 'social-settings', 'payments-general', 'currencies', 'discounts',
            'gateways', 'prices', 'pricing-rules', 'taxes', 'user-fields', 'user-registration', 'form-settings', 'marketplace',
            'user-roles', 'api-documentation', 'api-keys', 'api-integrations', 'webhooks', 'audit-log', 'shortcut-settings',
            'settings-tags', 'settings-theme', 'judging-rounds-settings', 'scoring-criteria-settings', 'action-tasks', 'zapier'];

        $expectedEnterLinks = ['my-entries', 'action-tasks'];

        $this->assertArrayNotHasKey('judge', $menuItemNames);
        $this->assertEmpty(array_diff($menuItemNames['manage'], $expectedManageLinks));
        $this->assertEmpty(array_diff($expectedManageLinks, $menuItemNames['manage']));

        $this->assertEmpty(array_diff($menuItemNames['enter'], $expectedEnterLinks));
        $this->assertEmpty(array_diff($expectedEnterLinks, $menuItemNames['enter']));
    }

    public function testChapterManagerMenu(): void
    {
        $user = $this->setupUserWithRole('Chapter manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $expectedManageLinks = ['manage-entries', 'judging-progress', 'users', 'settings-tags',
            'assignment-settings', 'panels-settings'];

        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayNotHasKey('judge', $menuItemNames);

        $this->assertEmpty(array_diff($menuItemNames['manage'], $expectedManageLinks));
        $this->assertEmpty(array_diff($expectedManageLinks, $menuItemNames['manage']));
    }

    public function testEntrantMenu(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $expectedEnterLinks = ['my-entries'];

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('judge', $menuItemNames);
        $this->assertArrayNotHasKey('guest', $menuItemNames);

        $this->assertEmpty(array_diff($menuItemNames['enter'], $expectedEnterLinks));
        $this->assertEmpty(array_diff($expectedEnterLinks, $menuItemNames['enter']));
    }

    public function testActionTasksEntrantMenu(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        Consumer::set(new UserConsumer($user));

        app()->instance(CurrentReviewTasks::class, $currentReviewTasks = m::mock(CurrentReviewTasks::class));
        $currentReviewTasks->shouldReceive('hasActiveReviewTasks')->andReturn(true);
        $currentReviewTasks->shouldReceive('pendingTasks')->andReturn(0);

        $menuItemNames = $this->generateUserMenuByContext();
        $expectedEnterLinks = ['my-entries', 'action-tasks'];

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('judge', $menuItemNames);
        $this->assertArrayNotHasKey('guest', $menuItemNames);

        $this->assertEmpty(array_diff($menuItemNames['enter'], $expectedEnterLinks));
        $this->assertEmpty(array_diff($expectedEnterLinks, $menuItemNames['enter']));
    }

    public function testJudgeMenu(): void
    {
        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $expectedJudgeLinks = ['judging-dashboard'];

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayNotHasKey('guest', $menuItemNames);

        $this->assertEmpty(array_diff($menuItemNames['judge'], $expectedJudgeLinks));
        $this->assertEmpty(array_diff($expectedJudgeLinks, $menuItemNames['judge']));
    }

    public function testActionTasksJudgeMenu(): void
    {
        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));

        app()->instance(CurrentReviewTasks::class, $currentReviewTasks = m::mock(CurrentReviewTasks::class));
        $currentReviewTasks->shouldReceive('hasActiveReviewTasks')->andReturn(true);
        $currentReviewTasks->shouldReceive('pendingTasks')->andReturn(0);

        $menuItemNames = $this->generateUserMenuByContext();
        $expectedJudgeLinks = ['judging-dashboard', 'action-tasks'];

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayNotHasKey('guest', $menuItemNames);

        $this->assertEmpty(array_diff($menuItemNames['judge'], $expectedJudgeLinks));
        $this->assertEmpty(array_diff($expectedJudgeLinks, $menuItemNames['judge']));
    }

    public function testManageTasksMenu(): void
    {
        $this->muffin(ReviewStage::class);
        $user = $this->setupUserWithRole('Program Manager');
        Consumer::set(new UserConsumer($user));

        $reviewTasks = m::mock(ReviewTaskRepository::class);
        $reviewTasks->shouldReceive('hasReviewTasks')->andReturn(true);

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('manage-tasks', $menuItemNames['manage']);
    }

    public function testGoodGrantsMenu(): void
    {
        $account = current_account();
        $account->updateAttributes(['brand' => Account::BRAND_GOODGRANTS], []);
        $account->defineFeatures(new Features([new Feature('grants', 'enabled')]));
        $account->defineFeatures(new Features([new Feature('fund_management', 'enabled')]));

        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $grantLinks = ['funding-allocation', 'contracts', 'funding-fund-list', 'funding', 'grant-shortcut-settings',
            'grant-status-list', 'manage-grants'];

        $this->assertEmpty(array_diff($grantLinks, $menuItemNames['manage']));
    }

    public function testGoodGrantsDisabledFundMenu(): void
    {
        $account = current_account();
        $account->updateAttributes(['brand' => Account::BRAND_GOODGRANTS], []);
        $account->defineFeatures(new Features([new Feature('grants', 'enabled')]));

        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $grantLinks = ['contracts', 'grant-shortcut-settings', 'grant-status-list', 'manage-grants'];

        $this->assertEmpty(array_diff($grantLinks, $menuItemNames['manage']));
    }

    public function testGoodGrantsInAwardsMenu(): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('grants', 'enabled')]));
        $account->defineFeatures(new Features([new Feature('fund_management', 'enabled')]));

        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $grantLinks = ['funding-allocation', 'contracts', 'funding-fund-list', 'funding', 'grant-status-list'];

        $this->assertEmpty(array_diff($grantLinks, $menuItemNames['manage']));
    }

    public function testProgramManagerMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Program Manager');
        Consumer::set(new UserConsumer($user));
        $contentBlockRoleIds = $user->roles->pluck('id')->toArray();
        $contentBlock1 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $contentBlock2 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['manage']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['manage']);
        $this->assertNotContains('about-page-'.$contentBlock1->id, $menuItemNames['enter']);
        //        $this->assertNotContains('about-page-'.$contentBlock2->id, $menuItemNames['guest']);
    }

    public function testEntrantMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        Consumer::set(new UserConsumer($user));
        $contentBlockRoleIds = $user->roles->pluck('id')->toArray();
        $contentBlock1 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $contentBlock2 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['enter']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['enter']);
    }

    public function testJudgeMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));
        $contentBlockRoleIds = $user->roles->pluck('id')->toArray();
        $contentBlock1 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $contentBlock2 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['judge']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['judge']);
    }

    public function testProgramManagerJudgeMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Judge');
        $programManagerRole = app(RoleSeedingService::class)->setupProgramManager(current_account());
        $judgeRole = $user->roles->first();
        app(UserRepository::class)->assignToRole($user, $programManagerRole->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        $user->setRelation('roles', new Roles([
            $programManagerRole,
            $judgeRole,
        ]));
        Consumer::set(new UserConsumer($user));
        $contentBlockRoleIds = [$programManagerRole->id];

        $contentBlock1 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $contentBlock2 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['manage']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['manage']);
        $this->assertNotContains('about-page-'.$contentBlock1->id, $menuItemNames['judge']);
        $this->assertNotContains('about-page-'.$contentBlock2->id, $menuItemNames['judge']);
    }

    public function testEntrantJudgeMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Judge');
        $entrantRole = app(RoleSeedingService::class)->setupEntrant(current_account());
        $judgeRole = $user->roles->first();
        app(UserRepository::class)->assignToRole($user, $entrantRole->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        $user->setRelation('roles', new Roles([
            $entrantRole,
            $judgeRole,
        ]));
        Consumer::set(new UserConsumer($user));
        $contentBlockRoleIds = [$entrantRole->id];

        $contentBlock1 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $contentBlock2 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['enter']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['enter']);
        $this->assertNotContains('about-page-'.$contentBlock1->id, $menuItemNames['judge']);
        $this->assertNotContains('about-page-'.$contentBlock2->id, $menuItemNames['judge']);
    }

    public function testBothEntrantJudgeMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Judge');
        $entrantRole = app(RoleSeedingService::class)->setupEntrant(current_account());
        $judgeRole = $user->roles->first();
        app(UserRepository::class)->assignToRole($user, $entrantRole->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        $user->setRelation('roles', new Roles([
            $entrantRole,
            $judgeRole,
        ]));
        Consumer::set(new UserConsumer($user));

        $contentBlock1 = $this->createContentBlockWithUserRoles([$entrantRole->id]);
        $contentBlock2 = $this->createContentBlockWithUserRoles([$judgeRole->id]);

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['enter']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['judge']);
    }

    public function testBothJudgeProgramManagerMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Judge');
        $programManager = app(RoleSeedingService::class)->setupProgramManager(current_account());
        $judgeRole = $user->roles->first();
        app(UserRepository::class)->assignToRole($user, $programManager->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        $user->setRelation('roles', new Roles([
            $programManager,
            $judgeRole,
        ]));
        Consumer::set(new UserConsumer($user));

        $contentBlock1 = $this->createContentBlockWithUserRoles([$programManager->id]);
        $contentBlock2 = $this->createContentBlockWithUserRoles([$judgeRole->id]);

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['manage']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['judge']);

        $this->assertNotContains('about-page-'.$contentBlock1->id, $menuItemNames['enter']);
        $this->assertNotContains('about-page-'.$contentBlock2->id, $menuItemNames['enter']);
    }

    public function testManagerGalleryMenu(): void
    {
        app()->instance(ContextGalleries::class, $contextGalleries = m::mock(ContextGalleries::class));
        $contextGalleries->shouldReceive('setContext->galleries')->andReturn(collect([1, 2]));

        $user = $this->setupUserWithRole('Program Manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('gallery-link-manage', $menuItemNames['manage']);
    }

    public function testEntrantGalleryMenu(): void
    {
        $this->contextService->setSelectedContext('enter');
        app()->instance(ContextGalleries::class, $contextGalleries = m::mock(ContextGalleries::class));
        $contextGalleries->shouldReceive('setContext->galleries')->andReturn(collect([1, 2]));

        $user = $this->setupUserWithRole('Entrant');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('gallery-link-enter', $menuItemNames['enter']);
    }

    public function testJudgeGalleryMenu(): void
    {
        app()->instance(ContextGalleries::class, $contextGalleries = m::mock(ContextGalleries::class));
        $contextGalleries->shouldReceive('setContext->galleries')->andReturn(collect([1, 2]));

        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('gallery-link-judge', $menuItemNames['judge']);
    }

    public function testSelectedContextSessionGalleryMenu(): void
    {
        app()->instance(ContextGalleries::class, $contextGalleries = m::mock(ContextGalleries::class));
        $contextGalleries->shouldReceive('setContext->galleries')->andReturn(collect([1, 2]));

        $user = $this->setupUserWithRole('Program Manager');
        $judgeRole = app(RoleSeedingService::class)->setupJudge(current_account());
        $programManagerRole = $user->roles->first();
        app(UserRepository::class)->assignToRole($user, $programManagerRole->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        Consumer::set(new UserConsumer($user));

        // The session is set to the first context item by default which is manage in this case
        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('gallery-link-manage', $menuItemNames['manage']);
        $this->assertNotContains('gallery-link-enter', $menuItemNames['enter']);
        $this->assertNotContains('gallery-link-judge', $menuItemNames['judge']);

        $this->contextService->setSelectedContext('enter');
        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertNotContains('gallery-link-manage', $menuItemNames['manage']);
        $this->assertContains('gallery-link-enter', $menuItemNames['enter']);
        $this->assertNotContains('gallery-link-judge', $menuItemNames['judge']);

        $this->contextService->setSelectedContext('judge');
        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertNotContains('gallery-link-manage', $menuItemNames['manage']);
        $this->assertNotContains('gallery-link-enter', $menuItemNames['enter']);
        $this->assertContains('gallery-link-judge', $menuItemNames['judge']);
    }

    public function testJudgeHasNoGalleryMenu(): void
    {
        app()->instance(ContextGalleries::class, $contextGalleries = m::mock(ContextGalleries::class));
        $contextGalleries->shouldReceive('setContext->galleries')->andReturn(collect());

        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertNotContains('gallery-link-judge', $menuItemNames['judge']);
    }

    public function testNoPermissionsGuestGalleryMenu(): void
    {
        $this->contextService->setSelectedContext('guest');

        $permissionCollection = new PermissionCollection([]);
        $role = $this->muffin(Role::class, ['guest' => true]);
        $role->setRelation('permissions', $permissionCollection);
        Consumer::set(new GuestConsumer(null));

        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_GALLERY]);
        $this->assignRolePanel($scoreSet, 4, $role);

        app(Synchroniser::class)->sync(new SyncFilter($scoreSet));

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('gallery-link-guest', $menuItemNames['guest']);
    }

    public function testNoPermissionsGalleryMenu(): void
    {
        $user = $this->muffin(User::class);
        $this->contextService->setSelectedContext('judge');

        $permissions = m::mock(PermissionRepository::class);
        $permissionCollection = new PermissionCollection([]);
        $permissions->shouldReceive('getByAccountAndUserId')->with(current_account()->id, $user->id)
            ->andReturn($permissionCollection);

        $role = $this->muffin(Role::class);
        $roles = m::mock(RoleRepository::class);
        $roles->shouldReceive('requestCache->getRolesForUser')->andReturn(collect([$role]));
        $role->setRelation('permissions', $permissionCollection);
        $user->setRelation('roles', new Roles([$role]));
        Consumer::set(new UserConsumer($user, $permissions, $roles));

        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_GALLERY]);
        $this->assignRolePanel($scoreSet, 4, $role);

        app(Synchroniser::class)->sync(new SyncFilter($scoreSet));

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertContains('gallery-link-judge', $menuItemNames['judge']);
    }

    public function testEnableFormFeatureMenu(): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('multiform', 'enabled')]));

        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('form', $menuItemNames['manage']);
    }

    public function testEnableOrdersFeatureMenu(): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('order_payments', 'enabled')]));
        $this->muffin(Setting::class, ['key' => 'paid-entries', 'value' => true]);
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('orders', $menuItemNames['manage']);
    }

    public function testEnableContractsFeatureMenu(): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('contracts', 'enabled')]));
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('primary-contracts', $menuItemNames['manage']);
    }

    public function testEnableContractsWithOtherGrantsFeatureMenu(): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('contracts', 'enabled')]));
        $account->defineFeatures(new Features([new Feature('grants', 'enabled')]));
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('contracts', $menuItemNames['manage']);
    }

    public function testDisableContractsFeatureMenu(): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('contracts', 'disabled')]));
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertNotContains('primary-contracts', $menuItemNames['manage']);
    }

    public function testEnableOrdersNoPaidEntriesFeatureMenu(): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('order_payments', 'enabled')]));

        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertNotContains('orders', $menuItemNames['manage']);
    }

    public function testGGDisablePaymentFeatureMenu(): void
    {
        $account = current_account();
        $account->updateAttributes(['brand' => Account::BRAND_GOODGRANTS], []);
        $account->defineFeatures(new Features([new Feature('order_payments', 'disabled')]));

        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $paymentLinks = ['currencies', 'discounts', 'payments-general', 'gateways', 'prices', 'pricing-rules', 'taxes'];

        $this->assertEquals(array_diff($paymentLinks, $menuItemNames['manage']), $paymentLinks);
    }

    public function testAccountOwnerZapierMenu(): void
    {
        $user = $this->setupUserWithRole('Judge');
        current_account()->setOwner($user);
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('zapier', $menuItemNames['manage']);
    }

    public function testGeneratedMenuStructure(): void
    {
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $menu = json_decode($this->view->baseMainMenu, true);
        $reviewFlow = array_filter($menu['children'], fn($filter) => $filter['name'] === 'review-flow');

        $settings = array_reduce($menu['children'], fn($r, $m) => $this->reduceMenuCallback($r, $m, 'settings'));
        $generalSettings = array_reduce($settings['children'], fn($r, $m) => $this->reduceMenuCallback($r, $m, 'general-settings'));

        $settingsAccount = array_filter($generalSettings['children'], fn($filter) => $filter['name'] === 'settings-account');
        $settingsLanguages = array_filter($generalSettings['children'], fn($filter) => $filter['name'] === 'settings-languages');

        $this->assertNotEmpty($reviewFlow);
        $this->assertNotEmpty($settings);
        $this->assertNotEmpty($generalSettings);
        $this->assertNotEmpty($settingsAccount);
        $this->assertNotEmpty($settingsLanguages);
    }

    public function testGuestJudgeDashboardMenu(): void
    {
        app()->instance(ContextJudging::class, $contextJudging = m::mock(ContextJudging::class));
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([collect([1, 2]), []]);
        $contextJudging->shouldReceive('setIsGuestContext')->andReturn($contextJudging);

        Consumer::set(new GuestConsumer(null));
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayNotHasKey('judge', $menuItemNames);
        $this->assertArrayHasKey('guest', $menuItemNames);

        $this->assertContains('judging-dashboard', $menuItemNames['guest']);
    }

    public function testGuestGalleryDashboardMenu(): void
    {
        app()->instance(ContextGalleries::class, $contextJudging = m::mock(ContextGalleries::class));
        $contextJudging->shouldReceive('setContext->galleries')->andReturn(collect([1, 2]));

        Consumer::set(new GuestConsumer(null));
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayNotHasKey('judge', $menuItemNames);
        $this->assertArrayHasKey('guest', $menuItemNames);

        $this->assertContains('gallery-link-guest', $menuItemNames['guest']);
    }

    public function testGuestAboutPageMenu(): void
    {
        $entrantRole = app(RoleSeedingService::class)->setupEntrant(current_account());
        $guestRole = $this->muffin(Role::class, ['guest' => true]);

        $contentBlock1 = $this->createContentBlockWithUserRoles([$guestRole->id]);
        $contentBlock2 = $this->createContentBlockWithUserRoles([$entrantRole->id]);
        $contentBlock3 = $this->createContentBlockWithUserRoles([$guestRole->id]);

        Consumer::set(new GuestConsumer(null));
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayNotHasKey('judge', $menuItemNames);
        $this->assertArrayHasKey('guest', $menuItemNames);

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['guest']);
        $this->assertContains('about-page-'.$contentBlock3->id, $menuItemNames['guest']);
        $this->assertNotContains('about-page-'.$contentBlock2->id, $menuItemNames['guest']);
    }

    public function testGuestNoGalleryJudgeDashboardMenu(): void
    {
        app()->instance(ContextJudging::class, $contextJudging = m::mock(ContextJudging::class));
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([collect([]), []]);
        $contextJudging->shouldReceive('setIsGuestContext')->andReturn($contextJudging);
        app()->instance(ContextGalleries::class, $contextGalleries = m::mock(ContextGalleries::class));
        $contextGalleries->shouldReceive('setContext->galleries')->andReturn(collect([]));

        Consumer::set(new GuestConsumer(null));
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayNotHasKey('manage', $menuItemNames);
        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayNotHasKey('judge', $menuItemNames);
        $this->assertArrayNotHasKey('guest', $menuItemNames);
    }

    public function testGuestProgramManagerMenuAboutPage(): void
    {
        $user = $this->setupUserWithRole('Program Manager');
        $guestRole = $this->muffin(Role::class, ['guest' => true]);

        Consumer::set(new UserConsumer($user));
        $contentBlockRoleIds = [$guestRole->id];

        $contentBlock1 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $contentBlock2 = $this->createContentBlockWithUserRoles($contentBlockRoleIds);

        app()->instance(ContextJudging::class, $contextJudging = m::mock(ContextJudging::class));
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([collect([1, 1]), []]);
        $contextJudging->shouldReceive('setIsGuestContext')->andReturn($contextJudging);
        app()->instance(ContextGalleries::class, $contextGalleries = m::mock(ContextGalleries::class));
        $contextGalleries->shouldReceive('setContext->galleries')->andReturn(collect([1, 2]));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['guest']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['guest']);

        $this->assertContains('about-page-'.$contentBlock1->id, $menuItemNames['guest']);
        $this->assertContains('about-page-'.$contentBlock2->id, $menuItemNames['guest']);

        $this->assertNotContains('judging-dashboard', $menuItemNames['guest']);

        $this->assertNotContains('about-page-'.$contentBlock1->id, $menuItemNames['manage']);
        $this->assertNotContains('about-page-'.$contentBlock2->id, $menuItemNames['manage']);
    }

    public function testLeaderboardJudgeMenu(): void
    {
        $user = $this->setupUserWithRole('Judge');
        $judgeRole = $user->roles->first();

        $leaderboardRole = $this->muffin(Role::class);
        $leaderboardRole->permissions()->save(new Permission(['resource' => 'ScoresAll', 'action' => 'view', 'mode' => new Mode('allow')]));
        $leaderboardRole->permissions()->save(new Permission(['resource' => 'EntriesAll', 'action' => 'view', 'mode' => new Mode('allow')]));

        app(UserRepository::class)->assignToRole($user, $leaderboardRole->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        $user->setRelation('roles', new Roles([
            $leaderboardRole,
            $judgeRole,
        ]));
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayHasKey('judge', $menuItemNames);
        $this->assertContains('judge-leaderboard', $menuItemNames['judge']);
    }

    public function testNoLeaderboardJudgeMenu(): void
    {
        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertNotContains($menuItemNames['judge'], ['judge-leaderboard']);
    }

    public function testAuditorCanSeeLeaderboard(): void
    {
        $user = $this->setupUserWithRole('Auditor');
        Consumer::set(new UserConsumer($user));

        $this->assertContains('leaderboard', $this->generateUserMenuByContext()['manage']);
    }

    public function testInitialSelectedContextEnter(): void
    {
        $this->contextService->setSelectedContext('enter');
        $this->initialSelectedContextTest();
    }

    public function testInitialSelectedContextGuestsNotExists(): void
    {
        $this->contextService->setSelectedContext('guest');
        $this->initialSelectedContextTest();
    }

    public function testInitialSelectedContextSessionNull(): void
    {
        $this->contextService->forgetSelectedContext();
        $this->initialSelectedContextTest();
    }

    public function testInitialSelectedContextSessionJudge(): void
    {
        $this->contextService->setSelectedContext('judge');

        $user = $this->setupUserWithRole('Judge');
        $entrant = app(RoleSeedingService::class)->setupEntrant(current_account());
        $judgeRole = $user->roles->first();
        app(UserRepository::class)->assignToRole($user, $entrant->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        $user->setRelation('roles', new Roles([
            $entrant,
            $judgeRole,
        ]));

        Consumer::set(new UserConsumer($user));
        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);

        $contexts = json_decode($this->view->contexts, true);
        $judgeContext = array_values(array_filter($contexts, fn($context) => $context['name'] == 'judge'))[0];

        $this->assertTrue($judgeContext['selected']);
    }

    public function testCustomRoleMenu(): void
    {
        $user = $this->muffin(User::class);

        $permissions = m::mock(PermissionRepository::class);
        $permissions->shouldReceive('getByAccountAndUserId')->with(current_account()->id, $user->id)
            ->andReturn(new PermissionCollection([
                new Permission(['resource' => 'EntriesOwner', 'action' => 'view', 'mode' => new Mode('allow')]),
                new Permission(['resource' => 'Users', 'action' => 'view', 'mode' => new Mode('allow')]),
            ]));

        $this->app->instance(PermissionRepository::class, $permissions);
        Consumer::set(new UserConsumer($user, $permissions));

        $menuItemNames = $this->generateUserMenuByContext();
        $expectedManageLinks = ['users', 'judging-progress'];
        $expectedEnterLinks = ['my-entries'];

        $this->assertArrayHasKey('manage', $menuItemNames);
        $this->assertArrayHasKey('enter', $menuItemNames);
        $this->assertEmpty(array_diff($menuItemNames['manage'], $expectedManageLinks));
        $this->assertEmpty(array_diff($menuItemNames['enter'], $expectedEnterLinks));
    }

    public function testCustomRoleAboutMenu(): void
    {
        $user = $this->muffin(User::class);

        $permissions = m::mock(PermissionRepository::class);
        $permissionCollection = new PermissionCollection([
            new Permission(['resource' => 'EntriesOwner', 'action' => 'view', 'mode' => new Mode('allow')]),
            new Permission(['resource' => 'Users', 'action' => 'view', 'mode' => new Mode('allow')]),
        ]);
        $permissions->shouldReceive('getByAccountAndUserId')->with(current_account()->id, $user->id)
            ->andReturn($permissionCollection);

        $role = new Role;
        $role->id = 42;
        $role->save();
        $roles = m::mock(RoleRepository::class);
        $roles->shouldReceive('requestCache->getRolesForUser')->andReturn(collect([$role]));

        $role->setRelation('permissions', $permissionCollection);
        $user->setRelation('roles', new Roles([$role]));
        Consumer::set(new UserConsumer($user, $permissions, $roles));

        $contentBlockRoleIds = $user->roles->pluck('id')->toArray();
        $contentBlock = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertContains('about-page-'.$contentBlock->id, $menuItemNames['manage']);
        $this->assertContains('about-page-'.$contentBlock->id, $menuItemNames['enter']);
    }

    public function testCustomRoleNoPermissionsAllRolesAboutMenu(): void
    {
        $user = $this->muffin(User::class);

        $permissions = m::mock(PermissionRepository::class);
        $permissions->shouldReceive('getByAccountAndUserId')->with(current_account()->id, $user->id)
            ->andReturn(new PermissionCollection([]));

        $this->app->instance(PermissionRepository::class, $permissions);
        Consumer::set(new UserConsumer($user, $permissions));

        $contentBlockRoleIds = $user->roles->pluck('id')->toArray();
        $contentBlock = $this->createContentBlockWithUserRoles($contentBlockRoleIds, 'all');
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayHasKey('enter', $menuItemNames);
        $this->assertEmpty(array_diff($menuItemNames['enter'], ['about-page-'.$contentBlock->id]));
    }

    public function testCustomRoleNoPermissionsAboutMenu(): void
    {
        $user = $this->muffin(User::class);

        $permissions = m::mock(PermissionRepository::class);
        $permissionCollection = new PermissionCollection([]);
        $permissions->shouldReceive('getByAccountAndUserId')->with(current_account()->id, $user->id)
            ->andReturn($permissionCollection);
        $this->app->instance(PermissionRepository::class, $permissions);

        $role = new Role;
        $role->id = 42;
        $role->save();
        $roles = m::mock(RoleRepository::class);
        $roles->shouldReceive('requestCache->getRolesForUser')->andReturn(collect([$role]));
        $role->setRelation('permissions', $permissionCollection);
        $user->setRelation('roles', new Roles([$role]));
        Consumer::set(new UserConsumer($user, $permissions, $roles));

        $contentBlockRoleIds = $user->roles->pluck('id')->toArray();
        $contentBlock = $this->createContentBlockWithUserRoles($contentBlockRoleIds);
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayHasKey('enter', $menuItemNames);
        $this->assertEmpty(array_diff($menuItemNames['enter'], ['about-page-'.$contentBlock->id]));
    }

    public function testSetEmptyContextRouteLink(): void
    {
        $user = $this->muffin(User::class);

        $permissions = m::mock(PermissionRepository::class);
        $permissions->shouldReceive('getByAccountAndUserId')->with(current_account()->id, $user->id)
            ->andReturn(new PermissionCollection([
                new Permission(['resource' => 'EntriesOwner', 'action' => 'view', 'mode' => new Mode('allow')]),
                new Permission(['resource' => 'Users', 'action' => 'view', 'mode' => new Mode('allow')]),
            ]));

        $this->app->instance(PermissionRepository::class, $permissions);
        Consumer::set(new UserConsumer($user, $permissions));
        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $decodedContexts = json_decode($this->view->contexts, true);

        $this->assertEquals(route('leaderboard.progress'), $decodedContexts[0]['route']);
    }

    public function testSetRedirectorContextRouteLink(): void
    {
        $user = $this->setupUserWithRole('Program Manager');

        Consumer::set(new UserConsumer($user));
        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $decodedContexts = json_decode($this->view->contexts, true);

        $this->assertEquals(route('guides-and-tours.index'), $decodedContexts[0]['route']);
    }

    public function testMenuItemsOrderPosition(): void
    {
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));
        $composer = m::mock(MainMenuComposer::class, [new BreadcrumbResolver, new FakeMainMenuGenerator($this->contextService)])->makePartial();
        $composer->compose($this->view);
        $menu = json_decode($this->view->baseMainMenu, true);

        $fakeMenuItems = $menu['children'];
        $this->assertEquals('fake-link-03', $fakeMenuItems[0]['name']);
        $this->assertEquals('fake-link-04', $fakeMenuItems[1]['name']);
        $this->assertEquals('fake-link-02', $fakeMenuItems[2]['name']);
        $this->assertEquals('fake-link-01', $fakeMenuItems[3]['name']);
        $this->assertEquals('fake-sub-menu', $fakeMenuItems[4]['name']);
        $fakeSubMenuItems = $fakeMenuItems[4]['children'];
        $this->assertEquals('fake-sub-link-02', $fakeSubMenuItems[0]['name']);
        $this->assertEquals('fake-sub-link-01', $fakeSubMenuItems[1]['name']);
        $this->assertEquals('fake-sub-link-03', $fakeSubMenuItems[2]['name']);
    }

    public function testEnterContextShouldNotDisplayToGuestUser(): void
    {
        $entrantRole = app(RoleSeedingService::class)->setupEntrant(current_account());
        $guestRole = $this->muffin(Role::class, ['guest' => true]);

        $this->createContentBlockWithUserRoles([$guestRole->id]);
        $this->createContentBlockWithUserRoles([$entrantRole->id], 'all');

        Consumer::set(new GuestConsumer(null));
        $menuItemNames = $this->generateUserMenuByContext();

        $this->assertArrayNotHasKey('enter', $menuItemNames);
        $this->assertArrayHasKey('guest', $menuItemNames);
    }

    #[TestWith([true])]
    #[TestWith([false])]
    public function testFormSettingItemVisibleInSingleFormAccounts(bool $featureEnabled): void
    {
        $account = current_account();
        $account->defineFeatures(new Features([new Feature('multiform', $featureEnabled ? 'enabled' : 'disabled')]));

        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);

        $menuItemNames = $this->generateUserMenuByContext();
        $this->assertSame(! $featureEnabled, in_array('form-settings', $menuItemNames['manage']));
    }

    private function initialSelectedContextTest()
    {
        $user = $this->setupUserWithRole('Judge');
        $entrant = app(RoleSeedingService::class)->setupEntrant(current_account());
        $judgeRole = $user->roles->first();
        app(UserRepository::class)->assignToRole($user, $entrant->id);
        app(UserRepository::class)->assignToRole($user, $judgeRole->id);
        $user->setRelation('roles', new Roles([
            $entrant,
            $judgeRole,
        ]));

        Consumer::set(new UserConsumer($user));
        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);

        $contexts = json_decode($this->view->contexts, true);
        $enterContext = array_values(array_filter($contexts, fn($context) => $context['name'] == 'enter'))[0];

        $this->assertTrue($enterContext['selected']);
    }

    private function generateUserMenuByContext(): array
    {
        $composer = new MainMenuComposer(new BreadcrumbResolver, $this->mainMenuGenerator());
        $composer->compose($this->view);
        $menu = json_decode($this->view->baseMainMenu, true);

        return $this->getMenuItemNamesByContext($menu);
    }

    private function mainMenuGenerator()
    {
        return new MainMenuGenerator($this->contextService);
    }

    private function getMenuItemNamesByContext(array $menu): array
    {
        if (isset($menu['children']) && count($menu['children']) > 0) {
            return collect($menu['children'])->reduce(function ($result, $item) {
                if (($item['type'] === 'link' || $item['type'] === 'shortcut_link') && isset($item['name'])) {
                    foreach ($item['contexts'] as $context) {
                        $result[$context][] = $item['name'];
                    }
                }

                if ($item['type'] === 'menu' && isset($item['children']) && count($item['children']) > 0) {
                    foreach ($this->getMenuItemNamesByContext($item) as $context => $menuItemNames) {
                        foreach ($menuItemNames as $menuItemName) {
                            $result[$context][] = $menuItemName;
                        }
                    }
                }

                return $result;
            }, []);
        }

        return [];
    }

    private function createContentBlockWithUserRoles($roleIds, $roleVisibility = 'select')
    {
        $contentBlock = $this->muffin(ContentBlock::class, ['key' => 'about-page', 'role_visibility' => $roleVisibility]);
        $contentBlock->syncRoles($roleIds);

        return $contentBlock;
    }

    private function reduceMenuCallback($result, $menu, $name)
    {
        if ($menu['name'] === $name) {
            return $menu;
        }

        return $result;
    }
}
