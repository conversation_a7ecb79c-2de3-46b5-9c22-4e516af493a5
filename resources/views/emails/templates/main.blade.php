<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html lang="{{ html_lang() }}" dir="{{ ltr_rtl_dir() }} xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width">

    <style>
        @media only screen and (max-width: 600px) {
            table[class="body"] .right-text-pad {
                padding-left: 10px !important;
            }
            table[class="body"] .left-text-pad {
                padding-right: 10px !important;
            }
        }
    </style>
    <style>
        @media only screen and (max-width: 600px) {
            table[class="body"] .right-text-pad {
                padding-left: 10px !important;
            }
            table[class="body"] .left-text-pad {
                padding-right: 10px !important;
            }
        }
    </style>
    <style>
        @media screen and (min-width: 580px) {
            .email-header,
            .email-body,
            .email-footer-image {
                width: 580px !important;
            }
        }
    </style>
</head>

<body style="-ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; color: #222222; font-family: 'Helvetica','Arial',sans-serif; font-size: 14px; font-weight: normal; line-height: 19px; margin: 0; min-width: 100%; padding: 0; text-align: {{ ltr_rtl_align() }} width: 100% !important; direction: {{ ltr_rtl_dir() }}">
<style>
    @media only screen and (max-width: 600px) {
        table[class="body"] img {
            width: auto !important;
            height: auto !important;
        }
        table[class="body"] center {
            min-width: 0 !important;
        }
        table[class="body"] .container {
            width: 95% !important;
        }
        table[class="body"] .row {
            width: 100% !important;
            display: block !important;
        }
        table[class="body"] .wrapper {
            display: block !important;
            padding-right: 0 !important;
        }
        table[class="body"] .columns,
        table[class="body"] .column {
            table-layout: fixed !important;
            float: none !important;
            width: 100% !important;
            padding-right: 0px !important;
            padding-left: 0px !important;
            display: block !important;
        }
        table[class="body"] .wrapper.first .columns,
        table[class="body"] .wrapper.first .column {
            display: table !important;
        }
        table[class="body"] table.columns td,
        table[class="body"] table.column td {
            width: 100% !important;
        }
        table[class="body"] .columns td.one,
        table[class="body"] .column td.one {
            width: 8.333333% !important;
        }
        table[class="body"] .columns td.two,
        table[class="body"] .column td.two {
            width: 16.666666% !important;
        }
        table[class="body"] .columns td.three,
        table[class="body"] .column td.three {
            width: 25% !important;
        }
        table[class="body"] .columns td.four,
        table[class="body"] .column td.four {
            width: 33.333333% !important;
        }
        table[class="body"] .columns td.five,
        table[class="body"] .column td.five {
            width: 41.666666% !important;
        }
        table[class="body"] .columns td.six,
        table[class="body"] .column td.six {
            width: 50% !important;
        }
        table[class="body"] .columns td.seven,
        table[class="body"] .column td.seven {
            width: 58.333333% !important;
        }
        table[class="body"] .columns td.eight,
        table[class="body"] .column td.eight {
            width: 66.666666% !important;
        }
        table[class="body"] .columns td.nine,
        table[class="body"] .column td.nine {
            width: 75% !important;
        }
        table[class="body"] .columns td.ten,
        table[class="body"] .column td.ten {
            width: 83.333333% !important;
        }
        table[class="body"] .columns td.eleven,
        table[class="body"] .column td.eleven {
            width: 91.666666% !important;
        }
        table[class="body"] .columns td.twelve,
        table[class="body"] .column td.twelve {
            width: 100% !important;
        }
        table[class="body"] td.offset-by-one,
        table[class="body"] td.offset-by-two,
        table[class="body"] td.offset-by-three,
        table[class="body"] td.offset-by-four,
        table[class="body"] td.offset-by-five,
        table[class="body"] td.offset-by-six,
        table[class="body"] td.offset-by-seven,
        table[class="body"] td.offset-by-eight,
        table[class="body"] td.offset-by-nine,
        table[class="body"] td.offset-by-ten,
        table[class="body"] td.offset-by-eleven {
            padding-left: 0 !important;
        }
        table[class="body"] table.columns td.expander {
            width: 1px !important;
        }
        table[class="body"] .right-text-pad,
        table[class="body"] .text-pad-right {
            padding-left: 10px !important;
        }
        table[class="body"] .left-text-pad,
        table[class="body"] .text-pad-left {
            padding-right: 10px !important;
        }
        table[class="body"] .hide-for-small,
        table[class="body"] .show-for-desktop {
            display: none !important;
        }
        table[class="body"] .show-for-small,
        table[class="body"] .hide-for-desktop {
            display: inherit !important;
        }
    }

    table {
        background-color: transparent;
    }

    caption {
        padding-top: 15px;
        padding-bottom: 15px;
        color: #d9d9d9;
        text-align: left;
    }

    th {
        text-align: left;
    }

    .table {
        width: 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }

    .table > thead > tr > th,
    .table > thead > tr > td,
    .table > tbody > tr > th,
    .table > tbody > tr > td,
    .table > tfoot > tr > th,
    .table > tfoot > tr > td {
        padding: 15px;
        line-height: 1.42857143;
        vertical-align: top;
        border-top: 1px solid #ddd;
    }

    .table > thead > tr > th {
        vertical-align: bottom;
        border-bottom: 2px solid #ddd;
    }

    .table > caption + thead > tr:first-child > th,
    .table > caption + thead > tr:first-child > td,
    .table > colgroup + thead > tr:first-child > th,
    .table > colgroup + thead > tr:first-child > td,
    .table > tbody.no-thead:first-child > tr:first-child > th,
    .table > tbody.no-thead:first-child > tr:first-child > td,
    .table > thead:first-child > tr:first-child > th,
    .table > thead:first-child > tr:first-child > td {
        border-top: 0;
    }

    .table > tbody + tbody {
        border-top: 2px solid #ddd;
    }

    .table .table {
        background-color: #fff;
    }

    .table-condensed > thead > tr > th,
    .table-condensed > thead > tr > td,
    .table-condensed > tbody > tr > th,
    .table-condensed > tbody > tr > td,
    .table-condensed > tfoot > tr > th,
    .table-condensed > tfoot > tr > td {
        padding: 7px;
    }

    .table-bordered {
        border: 1px solid #ddd;
    }

    .table-bordered > thead > tr > th,
    .table-bordered > thead > tr > td,
    .table-bordered > tbody > tr > th,
    .table-bordered > tbody > tr > td,
    .table-bordered > tfoot > tr > th,
    .table-bordered > tfoot > tr > td {
        border: 1px solid #ddd;
    }

    .table-bordered > thead > tr > th,
    .table-bordered > thead > tr > td {
        border-bottom-width: 2px;
    }

    .table-striped > tbody > tr:nth-of-type(odd) {
        background-color: #f9f9f9;
    }

    .table-hover > tbody > tr:hover {
        background-color: #f5f5f5;
    }

    table col[class*="col-"] {
        position: static;
        float: none;
        display: table-column;
    }

    table td[class*="col-"],
    table th[class*="col-"] {
        position: static;
        float: none;
        display: table-cell;
    }

    /* Table field preview */
    .table-field-preview-container .panel, .table-field-preview-container .duplicate-panel {
        border: none;
        margin: 0;
    }

    .table-field-preview-container .panel .panel-body, .table-field-preview-container .duplicate-panel .panel-body {
        padding: 0;
        margin: 0 0 10px 0;
    }

    .table-field-preview {
        margin-bottom: 0;
    }

    .table-field-preview tbody .index {
        width: 40px;
        min-width: 40px;
    }

    .table-field-preview tbody .cell-integer,
    .table-field-preview tbody .cell-decimal,
    .table-field-preview tbody .cell-decimal-precise {
        text-align: right;
    }

    .table-field-preview tbody tr:last-child {
        border-bottom: 1px solid #ddd;
    }
</style>
<table class="body" style="border-collapse: collapse; border-spacing: 0; color: #222222; font-family: 'Helvetica','Arial',sans-serif; font-size: 14px; font-weight: normal; height: 100%; line-height: 19px; margin: 0; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 100%;">
    <tbody>
    <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
        <td class="center" align="center" valign="top" style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
            <center style="background-color: #FAFAFA; min-width: 580px; padding-bottom: 20px; padding-top: 20px; width: 100%;">
                <table class="container" id="main-container" style="background-color: #FAFAFA; border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; padding-bottom: 20px; padding-top: 20px; text-align: inherit; vertical-align: top; width: 580px;">
                    <tbody>
                    <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                        <td style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                            @if ($headerImage)
                                <table class="row email-header" style="background-color: #FAFAFA; border-collapse: collapse; border-left: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; border-spacing: 0; border-top: 1px solid #DDDDDD; display: block; max-width: 580px !important; padding: 0px; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 100%;">
                                    <tbody>
                                    <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                        <td class="wrapper last" style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; padding-top: 0; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                            <table class="twelve columns" style="border-collapse: collapse; border-spacing: 0; margin: 0 auto; max-width: 580px !important; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 580px;">
                                                <tbody>
                                                <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                                    <td style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 0px 0px 10px; padding-bottom: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                                        <img src="{{ imgix($headerImage, params: config('ui.images.email.header')) }}" alt="Email header image" style="-ms-interpolation-mode: bicubic; clear: both; display: block; float: left; max-width: 100%; outline: none; text-decoration: none; width: auto;">
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            @endif
                            <table class="row email-body" style="background-color: #FFFFFF; border: 1px solid #DDDDDD; border-collapse: collapse; border-spacing: 0; display: block; max-width: 580px !important; padding: 0px; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 100%;">
                                <tbody>
                                <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                    <td class="wrapper last" style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; padding-top: 0; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                        <table class="twelve columns" style="border-collapse: collapse; border-spacing: 0; margin: 0 auto; max-width: 580px !important; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 580px;">
                                            <tbody>
                                            <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                                <td id="main-content" style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 20px; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                                    {!! $content !!}
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            @if ($footerImage)
                                <table class="row footer email-footer-image" style="background-color: #FAFAFA; border-bottom: 1px solid #DDDDDD; border-collapse: collapse; border-left: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; border-spacing: 0; display: block; max-width: 580px !important; padding: 0px; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 100%;">
                                    <tbody>
                                    <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                        <td class="wrapper last" style="-moz-hyphens: auto; -webkit-hyphens: auto; background: #ebebeb; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; padding-top: 0; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                            <table class="twelve columns" style="border-collapse: collapse; border-spacing: 0; margin: 0 auto; max-width: 580px !important; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 580px;">
                                                <tbody>
                                                <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                                    <td style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 0px 0px 10px; padding-bottom: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                                        <img src="{{ imgix($footerImage, params: config('ui.images.email.footer')) }}" alt="Email header image" style="-ms-interpolation-mode: bicubic; clear: both; display: block; float: left; max-width: 100%; outline: none; text-decoration: none; width: auto;">
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            @endif
                            @if ($footerContent)
                                <table class="row footer email-footer-text" style="border-collapse: collapse; border-spacing: 0; display: block; max-width: 580px !important; padding: 0px; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 100%;">
                                    <tbody>
                                    <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                        <td class="wrapper last" style="-moz-hyphens: auto; -webkit-hyphens: auto; background: #ebebeb; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; padding-top: 0; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                            <table class="twelve columns" style="border-collapse: collapse; border-spacing: 0; margin: 0 auto; max-width: 580px !important; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 580px;">
                                                <tbody>
                                                <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                                    <td class="footer-content" style="-moz-hyphens: auto; -webkit-hyphens: auto; background-color: #FAFAFA; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 13px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 0px 0px 10px; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                                        {!! $footerContent !!}
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            @endif
                            @if (!$systemNotification)
                                <table class="row footer email-footer-text" style="border-collapse: collapse; border-spacing: 0; display: block; max-width: 580px !important; padding: 0px; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 100%;">
                                    <tbody>
                                    <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                        <td class="wrapper last" style="-moz-hyphens: auto; -webkit-hyphens: auto; background: #ebebeb; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; padding-top: 0; position: relative; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                            <table class="twelve columns" style="border-collapse: collapse; border-spacing: 0; margin: 0 auto; max-width: 580px !important; padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top; width: 580px;">
                                                <tbody>
                                                <tr style="padding: 0; text-align: {{ ltr_rtl_align() }} vertical-align: top;">
                                                    <td class="footer-content" style="-moz-hyphens: auto; -webkit-hyphens: auto; background-color: #FAFAFA; border-collapse: collapse !important; color: #666666; font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 13px; font-weight: normal; hyphens: auto; line-height: 150%; margin: 0; padding: 0px 0px 10px; text-align: {{ ltr_rtl_align() }} vertical-align: top; word-break: break-word;">
                                                        {{ $unsubscribeLink }}
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            @endif
                        </td>
                    </tr>
                    </tbody>
                </table>
            </center>
        </td>
    </tr>
    </tbody>
</table>

</body>

</html>
