<?php $searching = query_parameters([
    'chapter',
    'category',
    'score-set',
    'assignment-method',
    'assignment_status',
    'state',
    'judge',
    'panel',
    'tag',
]); ?>

@section('title')
    {!! HTML::pageTitle(trans('assignments.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <assignment-index inline-template>
        <assignment-list id="assignment-list" :ids="@js($assignmentIds)" inline-template>
            <div id="searchResults">
                <div class="selectors island">
                    <div class="selector-title">
                        <div class="mrx">
                            <h1>{!! trans('assignments.titles.main') !!}</h1>
                            @include('partials.holocron.feature-intro-revealer')
                        </div>
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}

                        <div class="block">
                            <div class="inline-block">
                                @include('partials.list-actions.add-resource', ['label' => trans('assignments.titles.new'), 'route' => route('assignment.new')])
                            </div>
                            <div class="inline-block">
                                @include('partials.list-actions.add-resource', ['label' => trans('assignments.titles.new_random'), 'route' => route('assignment.new-random')])
                            </div>
                        </div>
                    </div>

                    @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                        @slot('actions')
                            @if ($canExport)
                                {!! HTML::exportAction('assignments.export') !!}
                            @endif
                            {!! HTML::broadcast('broadcast.new', 'assignments') !!}
                        @endslot
                    @endcomponent
                </div>

                <saved-views-shortcuts-bar
                    area="{{ $area }}"
                    title="{{ trans('search.shortcuts-bar.title') }}"
                    :saved-views="@js($savedViews)"
                    class="shortcuts-bar-space-above"
                ></saved-views-shortcuts-bar>


                @include('partials.errors.display')
                @include('partials.errors.message')

                <portal-target name="tagger" v-if="taggerRevealed"></portal-target>
                <portal-target name="untagger" v-if="untaggerRevealed"></portal-target>

                <div class="row mtm">
                    <div class="col-xs-12 col-lg-6 entry-selectors">
                        <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                            <ul class="action-list">
                                <li>
                                    @include('partials.list-actions.delete', [
                                    'resource' => 'assignment',
                                    'validIds' => $nonAutoAssignmentIds,
                                    'labels' => ['invalidSelection' => trans('assignments.delete.messages.invalid_selection')],
                                ])
                                </li>
                                <li class="divider"></li>
                                <li>
                                    @include('partials.list-actions.tag', ['resource' => 'assignment', 'labels' => ['button' => trans('buttons.add_entry_tags')]])
                                </li>
                                <li>
                                    @include('partials.list-actions.untag', ['resource' => 'assignment', 'labels' => ['button' => trans('buttons.remove_entry_tags')]])
                                </li>
                                <li class="divider"></li>
                                <li>
                                    @include('partials.list-actions.recuse', ['resource' => 'assignment'])
                                </li>
                                <li>
                                    @include('partials.list-actions.unrecuse', ['resource' => 'assignment'])
                                </li>
                            </ul>
                        </list-action-dropdown>
                    </div>
                    <div class="col-xs-12 col-lg-6">
                        <div class="search-info">
                            @include('partials.page.active-filters', ['filters' => request()->all()])
                            @include('partials.page.pagination-info', ['paginator' => $assignments])
                        </div>
                    </div>
                </div>

                <!-- Result set -->
                <div>
                    @if ($assignments->count())
                        @include('search.datatable', ['columnator' => $columnator, 'results' => $assignments->items(), 'class' => 'assignments-table'])

                        <div class="row">
                            <div class="col-xs-12">
                                @include('partials.page.pagination', ['paginator' => $assignments])
                            </div>
                        </div>
                    @else
                        <div>
                            <p>@lang('assignments.table.empty')</p>
                        </div>
                    @endif
                </div>
            </div>
        </assignment-list>
    </assignment-index>
@stop
