<template>
	<div :class="['app', 'form-group', { 'field-container-has-hint-text': showHint }]">
		<field-label v-if="displayLabel" :field="field" :for="fieldId"></field-label>
		<help-icon v-if="showHelpIcon" :content="field.helpText" />
		<validator
			:field-id="fieldId"
			:field-type="field.type"
			:field-value="field.value"
			:label="field.label"
			:required="field.required"
			:validate-on-blur="validateEvents"
			:validate-on-change="validateEvents"
			:validate-on-mount="hasError(fieldId)"
			:validate-on-value="validateOnValue"
		>
			<div>
				<component
					:is="fieldComponent"
					:id="fieldId"
					:element-id="fieldId"
					:field="field"
					:in-active-tab="true"
					:mode="field.mode"
					:name="field.name"
					:required="field.required"
					:resource-slug="consumer.slug"
					:value="field.value"
					:disabled="!field.writeAccess"
					@input="onInput"
					v-on="$listeners"
				/>
			</div>

			<instant-errors></instant-errors>
		</validator>
		<field-hint v-if="showHint" :hint-text="field.hintText" wrapper-classes="field-container-has-hint-text"></field-hint>
	</div>
</template>

<script>
import components, { fieldTypes } from '@/lib/components/Fields';
import Validator from '@/lib/components/Fields/validator/Validator';
import InstantErrors from '@/lib/components/Fields/validator/InstantErrors';
import FieldLabel from '@/lib/components/Fields/FieldLabel';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';
import featuresMixin from '@/lib/components/Shared/mixins/features-mixin';
import linksMixin from '@/lib/components/Shared/mixins/links-mixin';
import { mapGetters } from 'vuex';
import HelpIcon from '@/lib/components/Shared/HelpIcon.vue';
import FieldHint from '@/lib/components/Shared/FieldHint.vue';

export default {
	components: {
		FieldHint,
		HelpIcon,
		FieldLabel,
		Validator,
		InstantErrors,
		...components,
	},
	mixins: [langMixin, featuresMixin, linksMixin],
	props: {
		field: {
			type: Object,
			required: true,
		},
		errors: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		...mapGetters('authentication', ['consumer']),
		...mapGetters('validation', ['hasError']),
		fieldId() {
			return 'values.' + this.field.slug;
		},
		fieldComponent() {
			return fieldTypes[this.field.type];
		},
		displayLabel() {
			return this.field.type !== 'checkbox' && this.field.type !== 'content';
		},
		rules() {
			return this.field.required ? ['required'] : [];
		},
		validateEvents() {
			return this.field.type !== 'phone' && this.field.type !== 'file';
		},
		validateOnValue() {
			return this.field.type === 'file';
		},
		showHint() {
			return !!this.field.hintText;
		},
		showHelpIcon() {
			return !!this.field.helpText;
		},
	},
	methods: {
		onInput(value) {
			// eslint-disable-next-line vue/no-mutating-props
			this.field.value = value;
			this.$emit('input', value);
		},
	},
};
</script>
