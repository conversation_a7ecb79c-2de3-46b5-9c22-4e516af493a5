<template>
	<div ref="element">
		<slot name="default" v-bind="binded"></slot>
		<div class="panel panel-default panel-body">
			<div class="panel-title">
				<h4>{{ helpText }}</h4>
			</div>
			<template v-for="(mergeField, i) in mergeFields">
				<span :key="`merge-field-${i}`">
					<!-- eslint-disable-next-line vue/html-closing-bracket-newline Avoids create extra space between link and comma -->
					<a href @click.prevent="insert(mergeField)">{{ format(mergeField) }}</a
					><span>, </span>
				</span>
			</template>
		</div>
		<slot name="extra-field" v-bind="binded"></slot>
	</div>
</template>

<script lang="ts">
import Multilingual from '@/lib/components/Translations/Multilingual.vue';
import { defineComponent, PropType } from 'vue';
import { mergeFieldsController } from '@/lib/components/Shared/MergeFields/MergeFields.controller';

export default defineComponent({
	components: {
		Multilingual,
	},

	props: {
		mergeFields: {
			type: Array as unknown as PropType<string[]>,
			default: () => [],
		},
		helpText: {
			type: String,
			default: 'Available merge fields:',
		},
		targetElements: {
			type: Array,
			default: () => ['textarea', 'input'],
		},
	},

	setup: mergeFieldsController,
});
</script>

<style>
.panel-title h4 {
	font-size: 11pt;
	font-weight: bold;
}
</style>
