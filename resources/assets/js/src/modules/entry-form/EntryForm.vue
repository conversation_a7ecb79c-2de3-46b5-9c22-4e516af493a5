<template>
	<div>
		<ajax-errors />
		<tab-buttons :aria-orientation="ariaOrientation" :loaded="tabsLoaded" @selected="onTabSelected">
			<collaborators-control v-if="isCollaborative" :form="form" />
			<collaborators-control-disabled :form="form" />
		</tab-buttons>
		<tab-container :key="tabContainerKey" :loaded="isLoaded" :transitions="transitions" :configure="configure" />
		<configuration-tray v-if="configurationMode" :can-edit-fields="canEditFields" :can-edit-forms="canEditForms" />
		<submitted-broadcast-dialog />
		<ineligible-dialog />
	</div>
</template>

<script>
import $ from 'jquery';
import PusherClass from 'pusher-js';
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex';
import breakpoints from '@/lib/navigation-breakpoints.js';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';
import currenciesMixin from '@/lib/components/Fields/mixins/currencies-mixin';
import TabButtons from './Tabs/TabButtons.vue';
import TabContainer from './Tabs/TabContainer.vue';
import AjaxErrors from './Fields/AjaxErrors';
import ConfigurationTray from './Configuration/ConfigurationTray';
import featuresMixin from '@/lib/components/Shared/mixins/features-mixin.js';
import linksMixin from '@/lib/components/Shared/mixins/links-mixin.js';
import calculationFunctionMixin from '@/lib/components/Fields/mixins/calculation-functions-mixin.js';
import CollaboratorsControl from '@/modules/entry-form/Collaboration/CollaboratorsControl.vue';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import CollaboratorsControlDisabled from '@/modules/entry-form/Collaboration/CollaboratorsControlDisabled.vue';
import { submittableFormBus, SubmittableFormSignal } from '@/modules/entry-form/Signals';
import SubmittedBroadcastDialog from '@/modules/entry-form/Collaboration/SubmittedBroadcastDialog.vue';
import IneligibleDialog from '@/modules/entry-form/Collaboration/IneligibleDialog.vue';

const toastr = require('toastr');

export default {
	components: {
		IneligibleDialog,
		SubmittedBroadcastDialog,
		CollaboratorsControlDisabled,
		CollaboratorsControl,
		TabButtons,
		TabContainer,
		AjaxErrors,
		ConfigurationTray,
	},
	mixins: [langMixin, featuresMixin, linksMixin, currenciesMixin, calculationFunctionMixin],
	props: {
		configure: {
			type: Boolean,
			default: false,
		},
		user: {
			type: Object,
			default: null,
		},
		isManager: {
			type: Boolean,
			default: false,
		},
		hasManagerRole: {
			type: Boolean,
			default: false,
		},
		season: {
			type: Object,
			default: null,
		},
		form: {
			type: Object,
			required: true,
		},
		awaitingPayment: {
			type: Boolean,
			default: null,
		},
		canPay: {
			type: Boolean,
			default: false,
		},
		allowsDownloadBlankPdf: {
			type: Boolean,
			default: null,
		},
		chapters: {
			type: Array,
			required: true,
		},
		countries: {
			type: Object,
			required: true,
		},
		country: {
			type: String,
			default: null,
		},
		phoneWithLeadingZeros: {
			type: Array,
			default: () => [],
		},
		entry: {
			type: Object,
			default: null,
		},
		category: {
			type: Object,
			default: null,
		},
		lockedOnSubmission: {
			type: Boolean,
			default: null,
		},
		locks: {
			type: Object,
			required: true,
		},
		sponsorsEnabled: {
			type: Boolean,
			default: false,
		},
		paymentOnSubmit: {
			type: Boolean,
			default: null,
		},
		prepaymentRequired: {
			type: Boolean,
			default: null,
		},
		readOnly: {
			type: Boolean,
			default: null,
		},
		resubmissionPossible: {
			type: Boolean,
			default: null,
		},
		showSubmitOnFirstTab: {
			type: Boolean,
			default: null,
		},
		submissionPossible: {
			type: Boolean,
			default: null,
		},
		tab: {
			type: Object,
			default: null,
		},
		categories: {
			type: Array,
			default: () => [],
		},
		supportedLanguages: {
			type: Array,
			default: () => [],
		},
		plagiarismDetection: {
			type: Boolean,
			default: false,
		},
		searchableFields: {
			type: Array,
			default: () => [],
		},
		fieldTypes: {
			type: Array,
			default: () => [],
		},
		fieldCompatibility: {
			type: Object,
			default: () => ({}),
		},
		conditionalFieldTypes: {
			type: Array,
			default: () => [],
		},
		conditionals: {
			type: Object,
			default: () => ({}),
		},
		protectionTypes: {
			type: Array,
			default: () => [],
		},
		fieldTemplate: {
			type: Object,
			default: () => ({}),
		},
		fileTypes: {
			type: Object,
			default: () => ({}),
		},
		tableFieldConfiguratorLabels: {
			type: Object,
			default: () => ({}),
		},
		tableFieldInputTypes: {
			type: Array,
			default: () => [],
		},
		pusherOptions: {
			type: String,
			required: true,
		},
		tabTemplate: {
			type: Object,
			default: () => ({}),
		},
		tabTypes: {
			type: Array,
			default: () => [],
		},
		contentBlocks: {
			type: Array,
			default: () => [],
		},
		categoryTemplate: {
			type: Object,
			default: () => ({}),
		},
		categoryMergeFields: {
			type: Array,
			default: () => [],
		},
		scoreSets: {
			type: Array,
			default: () => [],
		},
		attachmentTypes: {
			type: Object,
			default: () => ({}),
		},
		chapterManagers: {
			type: Array,
			default: () => [],
		},
		chapterTemplate: {
			type: Object,
			default: () => ({}),
		},
		isAtChapterLimit: {
			type: Boolean,
			default: false,
		},
		canConfigureCategories: {
			type: Boolean,
			default: false,
		},
		canConfigureChapters: {
			type: Boolean,
			default: false,
		},
		canEditForms: {
			type: Boolean,
			default: false,
		},
		canEditFields: {
			type: Boolean,
			default: false,
		},
		formTypes: {
			type: Array,
			required: true,
		},
		visibleSelectors: {
			type: Boolean,
			default: true,
		},
		visibleTitle: {
			type: Boolean,
			default: true,
		},
		chapterVisible: {
			type: Boolean,
			default: false,
		},
		eligibleContentBlocks: {
			type: Array,
			default: () => [],
		},
		ineligibleContentBlocks: {
			type: Array,
			default: () => [],
		},
		eligibleNotifications: {
			type: Array,
			default: () => [],
		},
		ineligibleNotifications: {
			type: Array,
			default: () => [],
		},
		multiChapter: {
			type: Boolean,
			default: false,
		},
		showFormulaErrors: {
			type: Boolean,
			default: false,
		},
		multiForm: {
			type: Boolean,
			default: false,
		},
		refereeReviewStages: {
			type: Array,
			default: () => [],
		},
	},
	provide() {
		return {
			showFormulaErrors: this.showFormulaErrors,
		};
	},
	data() {
		return {
			transitions: true,
			autosaveInterval: null,
			isSticky: false,
			ariaOrientation: 'horizontal',
			stickyWatcher: null,
			isCollaborative: false,
			isUpdatable: false,
		};
	},
	computed: {
		...mapState('entryForm', ['status', 'tabsLoaded', 'formSessionUuid', 'tabs', 'chapterId', 'fields']),
		...mapGetters('entryForm', ['selectedTab', 'isLoaded', 'isIneligible']),
		...mapState('entryFormConfiguration', ['configurationMode']),
		tabContainerKey() {
			return 'tab-container-' + (this.configurationMode ? 'config-mode' : 'normal-mode');
		},
		transition() {
			return this.transitions ? 'push' : '';
		},
		tabComponent() {
			return this.selectedTab.type + 'Tab';
		},
	},
	watch: {
		status(newValue) {
			if (newValue.status === 'error') {
				toastr.options.onHidden = () => {
					this.storeStatus({ status: 'ok', message: '' });
				};

				toastr.error(this.lang.get(newValue.message));
			}
		},
		isLoaded(loaded) {
			if (loaded) {
				this.stickyWatcher = this.$watch(
					'tabs',
					(newValue) => {
						if (newValue.length < 10) {
							if (!this.isSticky) {
								this.applyStickyHeaderControls();
								this.isSticky = true;
							}
						} else {
							this.detachStickyHeaderControls();
							this.isSticky = false;
						}
					},
					{ deep: true }
				);
			}
		},
	},
	beforeCreate() {
		// Reset vuex store modules to default state
		this.$store.commit('global/resetState');
		this.$store.commit('entryForm/resetState');
		this.$store.commit('entryFormButtons/resetState');
		this.$store.commit('entryFormConfiguration/resetState');
		this.$store.commit('entryFormApi/resetState');
		this.$store.commit('entryForm/resetSubmittableType');
	},
	created() {
		this.storeGlobalState({
			supportedLanguages: Object.freeze(this.supportedLanguages),
		});
		this.storeGlobalState({ defaultLanguage: this.defaultLanguage });

		/** ToDo: switch routeSet for entrant / manager **/
		this.setRouteSet(this.isManager ? 'manager' : 'entrant');

		// Save countries in store
		let countries = this.countries;
		countries = Object.keys(countries).map((id) => ({
			id: id,
			name: countries[id],
		}));

		if (String.prototype.localeCompare) {
			const locale = this.language.replace('_', '-');
			countries = countries.sort((a, b) => a.name.localeCompare(b.name, locale));
		}

		this.storeGlobalState({ countries: Object.freeze(countries) });
		this.storeGlobalState({ country: this.country });
		this.storeGlobalState({ phoneWithLeadingZeros: this.phoneWithLeadingZeros });

		// Save buttons related data in store
		this.storeButtonsData(
			Object.freeze({
				awaitingPayment: this.awaitingPayment,
				canPay: this.canPay,
				lockedOnSubmission: this.lockedOnSubmission,
				paymentOnSubmit: this.paymentOnSubmit,
				prepaymentRequired: this.prepaymentRequired,
				readOnly: this.readOnly,
				resubmissionPossible: this.resubmissionPossible,
				submissionPossible: this.submissionPossible,
				ineligible: this.locks.ineligible,
			})
		);

		// Save entry in store
		if (this.entry) {
			this.storeEntry(this.entry);
		}

		this.storeEntryFormState({ season: this.season });
		this.storeEntryFormState({ form: this.form });
		this.storeEntryFormState({ isManager: this.isManager });
		this.storeEntryFormState({ hasManagerRole: this.hasManagerRole });
		this.storeEntryFormState({ locks: this.locks });

		if (this.chapters) {
			this.storeChapters(this.chapters);
		}

		if (this.category) {
			if (!this.chapterId) {
				this.setChapterId(this.category.chapters[0]);
			}

			this.storeCategory(this.category);
		}

		if (this.user) {
			this.storeUser(this.user);
		}

		this.storeEntryFormState({ awaitingPayment: this.awaitingPayment });
		this.storeEntryFormState({ canPay: this.canPay });
		this.storeEntryFormState({ allowsDownloadBlankPdf: this.allowsDownloadBlankPdf });
		this.storeEntryFormState({ sponsorsEnabled: this.sponsorsEnabled });
		this.storeEntryFormState({
			fieldGuideUrl: this.applicationLinks.get('fields_guide'),
		});
		this.storeEntryFormState({ categories: Object.freeze(this.categories) });
		this.storeEntryFormState({ plagiarismDetection: this.plagiarismDetection });
		this.storeEntryFormState({
			searchableFields: Object.freeze(this.searchableFields),
		});
		this.storeEntryFormState({
			fieldTypes: Object.freeze(this.fieldTypes),
		});
		this.storeEntryFormState({
			fieldCompatibility: Object.freeze(this.fieldCompatibility),
		});
		this.storeEntryFormState({
			conditionalFieldTypes: Object.freeze(this.conditionalFieldTypes),
		});
		this.storeEntryFormState({
			conditionals: Object.freeze(this.conditionals),
		});
		this.storeEntryFormState({
			protectionTypes: Object.freeze(this.protectionTypes),
		});
		this.storeEntryFormState({
			protectionGuideUrl: this.applicationLinks.get('protection_guide'),
		});
		this.storeEntryFormState({
			fieldTemplate: Object.freeze(this.fieldTemplate),
		});
		this.storeEntryFormState({ fileTypes: Object.freeze(this.fileTypes) });
		this.storeEntryFormState({
			tableFieldConfiguratorLabels: Object.freeze(this.tableFieldConfiguratorLabels),
		});
		this.storeEntryFormState({
			tableFieldInputTypes: Object.freeze(this.tableFieldInputTypes),
		});
		this.storeEntryFormState({
			tabTemplate: Object.freeze(this.tabTemplate),
		});
		this.storeEntryFormState({
			tabTypes: Object.freeze(this.tabTypes),
		});
		this.storeEntryFormState({
			contentBlocks: Object.freeze(this.contentBlocks),
		});
		this.storeEntryFormState({
			categoryTemplate: Object.freeze(this.categoryTemplate),
		});
		this.storeEntryFormState({
			categoryMergeFields: Object.freeze(this.categoryMergeFields),
		});
		this.storeEntryFormState({
			scoreSets: Object.freeze(this.scoreSets),
		});
		this.storeEntryFormState({
			attachmentTypes: Object.freeze(this.attachmentTypes),
		});
		this.storeEntryFormState({
			chapterManagers: Object.freeze(this.chapterManagers),
		});
		this.storeEntryFormState({
			chapterTemplate: Object.freeze(this.chapterTemplate),
		});
		this.storeEntryFormState({
			isAtChapterLimit: this.isAtChapterLimit,
		});
		this.storeEntryFormState({
			canConfigureCategories: this.canConfigureCategories,
		});
		this.storeEntryFormState({
			canConfigureChapters: this.canConfigureChapters,
		});
		this.storeEntryFormState({
			chapterVisible: this.chapterVisible,
		});

		this.storeEntryFormState({ form: this.form });
		this.storeEntryFormState({ formTypes: this.formTypes });

		this.storeEntryFormState({
			visibleSelectors: this.visibleSelectors,
		});

		this.storeEntryFormState({
			visibleTitle: this.visibleTitle,
		});

		this.storeEntryFormState({
			eligibleContentBlocks: Object.freeze(this.eligibleContentBlocks),
		});
		this.storeEntryFormState({
			ineligibleContentBlocks: Object.freeze(this.ineligibleContentBlocks),
		});
		this.storeEntryFormState({
			eligibleNotifications: Object.freeze(this.eligibleNotifications),
		});
		this.storeEntryFormState({
			ineligibleNotifications: Object.freeze(this.ineligibleNotifications),
		});

		this.storeEntryFormState({
			multiChapter: Object.freeze(this.multiChapter),
		});

		this.storeEntryFormState({
			multiForm: Object.freeze(this.multiForm),
		});

		this.storeEntryFormState({
			refereeReviewStages: Object.freeze(this.refereeReviewStages),
		});

		// Load initial data as soon as the form is created
		this.loadData();

		if (this.tab) {
			this.selectTab(this.tab.id);
		}

		if (!this.isManager) {
			this.autosaveInterval = setInterval(() => {
				this.handleAutosave();
			}, 25000);

			window.addEventListener('beforeunload', this.beforeunloadListener);
			$(document).on('pjax:start', this.beforeunloadListener);
		}

		this.createFormSessionUuid();
		this.pusherSubscribe();

		const { Submittable, FlagsSubmittable } = useEntryFormContainer();
		this.isCollaborative = Submittable.isCollaborative();
		this.isUpdatable = Submittable.isUpdatable();
		this.setAutosaveEnabled(!this.isCollaborative && !this.isUpdatable);

		if (this.locks.ineligible !== undefined) {
			FlagsSubmittable.service.init({
				isIneligible: this.locks.ineligible,
			});
		}

		FlagsSubmittable.service.subscribe((state) => {
			if (state && !this.isIneligible && state.isIneligible) {
				submittableFormBus.emit(SubmittableFormSignal.INELIGIBLE, state);
			}
		});
	},
	beforeDestroy() {
		if (this.stickyWatcher) {
			this.stickyWatcher();
		}

		if (this.mediaQuery) {
			this.mediaQuery.removeListener(this.stickyHeaderListener);
		}

		if (this.stickyBits) {
			this.stickyBits.cleanup();
		}

		if (this.autosaveInterval) {
			clearInterval(this.autosaveInterval);

			window.removeEventListener('beforeunload', this.beforeunloadListener);
			$(document).off('pjax:start', this.beforeunloadListener);
		}

		this.pusherUnsubscribe();
	},
	methods: {
		...mapMutations('entryForm', [
			'storeEntryFormState',
			'storeEntry',
			'storeCategory',
			'storeStatus',
			'storeChapters',
			'storeUser',
			'selectTab',
			'finishRecalculation',
			'createFormSessionUuid',
			'setChapterId',
		]),
		...mapMutations('entryFormButtons', ['storeButtonsData', 'updateButton']),
		...mapMutations('entryFormApi', ['setRouteSet', 'setAutosaveEnabled']),
		...mapMutations('global', ['storeGlobalState']),
		...mapActions('entryForm', ['loadData', '']),
		...mapActions('entryFormApi', ['autosave']),
		onTabSelected(tabId) {
			// Suppress transitions while switching tabs
			this.transitions = false;
			this.$store.commit('entryForm/selectTab', tabId);
			this.$nextTick(() => {
				this.transitions = true;
			});
		},
		stickyHeaderListener() {
			if (this.mediaQuery.matches) {
				this.ariaOrientation = 'horizontal';
				this.$nextTick(() => {
					this.stickyBits = $('.header-controls').stickybits({
						useStickyClasses: true,
					});
				});
			} else {
				this.ariaOrientation = 'vertical';
				if (this.stickyBits) {
					this.stickyBits.cleanup();
					this.stickyBits = null;
				}
			}
		},
		applyStickyHeaderControls() {
			// Use matchMedia to enable sticky header when the 'sm' breakpoint is reached.
			// This is more effective than detecting a mobile device or hooking into the
			// window 'resize' event, because it's triggered on window resize and also
			// on zoom in / zoom out.
			if (window.matchMedia) {
				this.mediaQuery = window.matchMedia(`(min-width: ${breakpoints.sm}px)`);
				this.mediaQuery.addListener(this.stickyHeaderListener);
				this.stickyHeaderListener(this.mediaQuery);
			}
		},
		detachStickyHeaderControls() {
			if (this.mediaQuery) {
				this.mediaQuery.removeListener(this.stickyHeaderListener);
			}

			if (this.stickyBits) {
				this.stickyBits.cleanup();
			}

			this.mediaQuery = null;
		},
		pusherSubscribe() {
			const options = this.pusherOptions.clarify();
			this.pusher = new PusherClass(options.appKey, options.options);
			this.consumerChannel = null;
			this.entryChannel = null;

			options.channels.forEach((channel) => {
				if (channel.indexOf('consumer') !== -1) {
					if (this.consumerChannel === null) {
						this.consumerChannel = this.pusher.subscribe(channel);
					}

					this.consumerChannel.bind('field_values.recalculation_completed', this.onRecalculationCompleted);
				}

				if (channel.indexOf('entry') !== -1) {
					if (this.entryChannel === null) {
						this.entryChannel = this.pusher.subscribe(channel);
					}

					this.entryChannel.bind('field_values.outdated', this.onOutdatedValues);
					this.entryChannel.bind(SubmittableFormSignal.SUBMITTED, (data) =>
						submittableFormBus.emit(SubmittableFormSignal.SUBMITTED, data)
					);
				}
			});
		},
		pusherUnsubscribe() {
			this.pusher.disconnect();
			this.pusher = null;
			this.consumerChannel = null;
		},
		onRecalculationCompleted(data) {
			if (data.slug) {
				this.finishRecalculation(data.slug);
			}
		},
		onOutdatedValues(data) {
			if (
				data.slug &&
				this.entry &&
				this.entry.slug === data.slug &&
				this.formId !== data.formId &&
				this.formSessionUuid !== data.formSessionUuid
			) {
				this.$store.commit(
					'entryForm/storeStatus',
					{
						status: 'error',
						message: this.lang.get('miscellaneous.outdated'),
					},
					{ root: true }
				);
			}
		},
		beforeunloadListener() {
			this.handleAutosave();
		},
		handleAutosave() {
			this.autosave();
			if (!window.navigator.onLine) toastr.error(this.lang.get('miscellaneous.alerts.no-connection'));
		},
	},
};
</script>
