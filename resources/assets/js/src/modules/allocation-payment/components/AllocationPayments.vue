<template>
	<modal
		ref="modal"
		v-model="modalIsOpen"
		:header="false"
		:footer="false"
		:close-with-escape="false"
		:confirm-on-enter="false"
		:close-on-confirm="false"
		:close-on-backdrop-clicks="false"
		:modal-dialog-classes="['modal-lg']"
		@backdrop-clicked="doneClicked"
	>
		<close-icon slot="before-content" :disabled="modeState.isFetching()" @click.prevent.stop="doneClicked"></close-icon>
		<h4 id="allocationPaymentsModalLabel" class="modal-title" v-text="modalTitle"></h4>
		<div class="row">
			<div class="col-xs-12">
				<div v-if="error || exceedsAllocationAmount" class="alert-error" role="alert">
					<div class="icon">
						<div class="af-icons-md af-icons-md-alert-error"></div>
					</div>
					<div class="message">
						<ul class="errors">
							<li v-if="exceedsAllocationAmount">
								{{ lang.get('allocation-payments.form.amount_exceed') }}
							</li>
							<li v-if="error">
								{{ error }}
							</li>
						</ul>
					</div>
				</div>

				<div v-show="!modalState.isCommentsMode()" class="allocated">
					<strong>
						{{
							lang.get('allocation-payments.form.allocated_amount', {
								currency: currency,
								amount: formattedAllocatedAmount,
							})
						}}
					</strong>
				</div>
				<div v-if="allocationPaymentsAreEmpty" class="allocation-payments-actions">
					<select-field
						v-if="mappedPaymentScheduleTemplates.length > 0"
						:items="mappedPaymentScheduleTemplates"
						value-property="name"
						id-property="slug"
						:empty-option="true"
						:placeholder="lang.get('allocation-payments.form.payment_schedule_template.placeholder')"
						:disabled="modeState.isFetching()"
						@selected="selectPaymentScheduleTemplate"
					/>
					<select-field
						v-else
						:items="emptyPaymentScheduleTemplates"
						value-property="name"
						id-property="key"
						:empty-option="false"
						:placeholder="lang.get('allocation-payments.form.payment_schedule_template.placeholder')"
					/>
					<span>{{ lang.get('allocation-payments.form.texts.or') }}</span>
					<a
						href="javascript:"
						role="button"
						:disabled="modeState.isFetching()"
						class="btn btn-secondary"
						@click.prevent="addAllocationPayment({}, true)"
					>
						<span>{{ lang.get('allocation-payments.form.buttons.add_payments_manually') }}</span>
					</a>
				</div>
				<table v-show="modalState.isPaymentsMode()" ref="table" class="table table-condensed table-allocation-payments">
					<thead v-if="!allocationPaymentsAreEmpty" class="allocation-payments-table-headers">
						<tr>
							<th></th>
							<th>{{ lang.get('allocation-payments.form.table.headers.method') }}</th>
							<th></th>
							<th>{{ lang.get('allocation-payments.form.table.headers.reference') }}</th>
							<th>{{ lang.get('allocation-payments.form.table.headers.date_due') }}</th>
							<th>{{ lang.get('allocation-payments.form.table.headers.date_paid') }}</th>
							<th class="text-right">{{ lang.get('allocation-payments.form.table.headers.status') }}</th>
							<th class="text-right">{{ lang.get('allocation-payments.form.table.headers.amount') }}</th>
						</tr>
					</thead>
					<tbody>
						<allocation-payment-row
							v-for="(allocationPayment, index) in form.allocationPayments"
							:key="index"
							v-model="form.allocationPayments[index]"
							:index="index"
							:statuses="statuses"
							:payment-methods="paymentMethods"
							:currency="currency"
							:locale="locale"
							:max-amount="getAllocationPaymentMaxAmount(index)"
							:permissions="permissions"
							:validation-errors="validationErrors"
							@reset-errors="resetErrors"
							@backup="backupAllocationPayment"
							@cancel-current-allocation-payment="cancelCurrentAllocationPayment"
							@delete="deleteAllocationPayment(index)"
							@comment="commentAllocationPayment(allocationPayment, index)"
						>
						</allocation-payment-row>
					</tbody>
					<tfoot>
						<tr ref="first-statistic-line" class="statistic-line">
							<td rowspan="4" colspan="6" style="vertical-align: top">
								<i v-if="modeState.isFetching()" class="af-icons af-icons-repeat af-icons-animate-rotate"></i>
								<a
									v-if="modeState.isEmptyMode() && !allocationPaymentsAreEmpty && !editOnlyAllocationPayment"
									href="javascript:"
									role="button"
									class="btn btn-secondary"
									@click.prevent="addAllocationPayment({}, true)"
								>
									<span>{{ lang.get('allocation-payments.form.buttons.add_payment') }}</span>
								</a>
								<a
									v-if="modeState.isEditMode()"
									href="javascript:"
									role="button"
									class="btn btn-secondary"
									@click.prevent="saveAllocationPayment"
								>
									<span>{{ lang.get('buttons.save') }}</span>
								</a>
								<a v-if="modeState.isEditMode()" href="javascript:" @click.prevent="cancelAllocationPayment">
									<span>{{ lang.get('buttons.cancel') }}</span>
								</a>
							</td>

							<td class="text-right statistics-line_label">
								<strong>
									{{ lang.get('allocation-payments.form.payments_scheduled') }}
								</strong>
							</td>

							<td class="text-right amount">
								<strong>
									{{ formattedPaymentsScheduledAmount }}
								</strong>
							</td>
						</tr>
						<tr class="statistic-line">
							<td class="text-right statistics-line_label">
								{{ lang.get('allocation-payments.form.unscheduled_balance') }}
							</td>
							<td class="text-right amount">
								{{ formattedUnscheduledBalance }}
							</td>
						</tr>
						<tr class="statistic-line">
							<td class="text-right statistics-line_label">
								{{ lang.get('allocation-payments.form.paid') }}
							</td>
							<td class="text-right amount">
								{{ formattedPaid }}
							</td>
						</tr>
						<tr class="statistic-line">
							<td class="text-right statistics-line_label">
								<strong>
									{{ lang.get('allocation-payments.form.amount_due') }}
								</strong>
							</td>
							<td class="text-right amount">
								<strong>
									{{ amountDue }}
								</strong>
							</td>
						</tr>
					</tfoot>
				</table>
				<comments
					v-if="modalState.isCommentsMode()"
					:style="commentsStyle"
					:read-only="false"
					:uploads="false"
					:comments="commentsConfiguration.comments"
					:create-url="allocationPaymentsCommentsGlobalConfiguration.routes.commentCreateUrl"
					:delete-url="allocationPaymentsCommentsGlobalConfiguration.routes.commentUpdateUrl"
					:update-url="allocationPaymentsCommentsGlobalConfiguration.routes.commentDeleteUrl"
					:token="commentsConfiguration.token"
					:labels="allocationPaymentsCommentsGlobalConfiguration.labels"
					:user-id="allocationPaymentsCommentsGlobalConfiguration.userId"
					:default-language="allocationPaymentsCommentsGlobalConfiguration.defaultLanguage"
					:language="allocationPaymentsCommentsGlobalConfiguration.language"
					:translations="translations"
					@added="commentAdded"
					@deleted="commentDeleted"
				></comments>
			</div>
		</div>
		<button
			v-if="modalState.isPaymentsMode()"
			type="button"
			class="btn btn-primary"
			:disabled="!canSaveAllocationPayments || modeState.isFetching()"
			@click.prevent.stop="doneClicked"
		>
			{{ lang.get('allocation-payments.form.buttons.done') }}
		</button>
		<button v-if="modalState.isCommentsMode()" type="button" class="btn btn-primary" @click.prevent.stop="backToPayments">
			{{ lang.get('allocation-payments.modal.comments.back_button') }}
		</button>
	</modal>
</template>

<script>
import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';
import { Modal, SelectField } from 'vue-bootstrap';
import AllocationPaymentRow from '@/modules/allocation-payment/components/AllocationPaymentRow.vue';
import AllocationPaymentType from '@/modules/allocation-payment/components/AllocationPaymentType';
import modeState from './states/mode.state';
import modalState from './states/modal-status.state';
import moment from 'moment-timezone';
import { mapActions, mapState } from 'vuex';
import Comments from '@/lib/components/Comments/Comments.vue';
import CloseIcon from '@/lib/components/ListActions/Partials/CloseIcon.vue';
import PaymentScheduleTemplate from '@/modules/payment-schedule-templates/PaymentScheduleTemplate';
import { formatMoney } from '@/lib/Money';

const tectoastr = require('tectoastr');

export default {
	name: 'AllocationPayments',

	components: {
		SelectField,
		AllocationPaymentRow,
		Modal,
		Comments,
		CloseIcon,
	},

	mixins: [langMixin],

	props: {
		allocationPayments: {
			type: Array,
			default: () => [],
		},
		routes: {
			type: Object,
			default: () => ({}),
		},
		paymentScheduleTemplates: {
			type: Array,
			default: () => [],
		},
		paymentMethods: {
			type: Array,
			default: () => [],
		},
		statuses: {
			type: Array,
			default: () => [],
		},
		locale: {
			type: String,
			required: true,
		},
		unitOptions: {
			type: Array,
			default: () => [],
		},
		permissions: {
			type: Object,
			default: () => ({}),
		},
		allocationPaymentsCommentsGlobalConfiguration: {
			type: Object,
			default: () => ({}),
		},
	},

	data() {
		return {
			modalState,
			modeState,
			modalClasses: ['modal', 'fade'],
			error: null,
			validationErrors: {},
			form: {
				allocationPayments: [],
				backupAllocationPayments: [],
			},
			allocatedAmount: null,
			allocationCreatedAt: null,
			grantEndDate: null,
			currency: null,
			commentsConfiguration: this.getDefaultCommentConfiguration(),
		};
	},

	computed: {
		...mapState('allocationPayments', ['allocation', 'modalIsOpen', 'editOnlyAllocationPayment']),
		mappedPaymentScheduleTemplates() {
			return this.paymentScheduleTemplates.map(
				(paymentScheduleTemplate) =>
					new PaymentScheduleTemplate(paymentScheduleTemplate, this.unitOptions, this.allocationCreatedAt, this.grantEndDate)
			);
		},

		emptyPaymentScheduleTemplates() {
			return [
				{
					key: null,
					name: this.lang.get('allocation-payments.form.payment_schedule_template.empty_option'),
					disabled: true,
				},
			];
		},

		commentsStyle() {
			return {
				maxHeight: '330px',
				overflowY: 'auto',
				position: 'relative',
				paddingRight: '15px',
			};
		},

		modalTitle() {
			if (this.modalState.isPaymentsMode()) {
				return this.lang.get('allocation-payments.modal.header');
			}

			return this.lang.get('allocation-payments.modal.comments.header');
		},

		currentAllocationPayment() {
			return this.form.allocationPayments?.[this.modeState.currentAllocationPaymentIndex];
		},

		isModalOpen() {
			return this.modalClasses.includes('in');
		},

		isEditLockedToSinglePayment() {
			return this.editOnlyAllocationPayment !== null;
		},

		allocationPaymentsAreEmpty() {
			return this.form.allocationPayments.length === 0;
		},

		formattedAllocatedAmount() {
			return this.formatAmount(parseFloat(this.allocatedAmount));
		},

		paymentsScheduledAmount() {
			return parseFloat(
				this.form.allocationPayments
					.filter((allocationPayment) => allocationPayment.status !== 'failed_permanently')
					.reduce((total, allocationPayment) => total + allocationPayment?.amount ?? 0, 0)
					.toFixed(2)
			);
		},

		formattedPaymentsScheduledAmount() {
			if (this.paymentsScheduledAmount === 0) {
				return '—';
			}

			return this.formatAmount(this.paymentsScheduledAmount);
		},

		unscheduledBalance() {
			const unscheduledBalance = this.allocatedAmount - this.paymentsScheduledAmount;

			if (unscheduledBalance < 0) {
				return 0;
			}

			return unscheduledBalance;
		},

		exceedsAllocationAmount() {
			return this.allocatedAmount - this.paymentsScheduledAmount < 0;
		},

		formattedUnscheduledBalance() {
			return this.formatAmount(this.unscheduledBalance);
		},

		paid() {
			const paid = this.form.allocationPayments.filter((allocationPayment) => allocationPayment.status === 'paid');
			return paid.reduce((total, allocationPayment) => total + allocationPayment.amount, 0);
		},

		formattedPaid() {
			if (this.paid === 0) {
				return '—';
			}

			return this.formatAmount(this.paid);
		},

		amountDue() {
			return this.formatAmount(this.allocatedAmount - this.paid);
		},

		canSaveAllocationPayments() {
			return !this.exceedsAllocationAmount;
		},
	},

	mounted() {
		this.registerAllocationMutationChange();
		window.addEventListener('keydown', this.keysListener);
		this.resetState();
	},

	beforeDestroy() {
		window.removeEventListener('keydown', this.keysListener);
		this.unsubscribe();
	},

	methods: {
		...mapActions('allocationPayments', ['resetState']),

		registerAllocationMutationChange() {
			this.unsubscribe = this.$store.subscribe((mutation) => {
				if (mutation.type === 'allocationPayments/setAllocation') {
					const allocation = mutation.payload;

					if (allocation) {
						const allocatedAmount = allocation.amount ?? null;
						this.allocatedAmount = parseFloat(allocatedAmount);
						this.allocationCreatedAt = allocation.created_at;
						this.grantEndDate = allocation.grantEndDate;
						this.currency = this.allocation?.currency ?? null;

						if (
							Object.prototype.hasOwnProperty.call(this.allocation ?? {}, 'allocationPayments') &&
							allocation.allocationPayments.length > 0
						) {
							allocation.allocationPayments.forEach((allocationPayment) => {
								this.form.allocationPayments.push(new AllocationPaymentType(allocationPayment));
							});

							this.backupAllocationPayments();
							this.openEditOnlyAllocationPayment();
						} else {
							this.fetchAllocationPayments().then(() => {
								this.backupAllocationPayments();
								this.openEditOnlyAllocationPayment();
							});
						}

						this.error = null;
						this.validationErrors = null;
					}
				}
			});
		},

		backupAllocationPayments() {
			this.form.backupAllocationPayments = this.getAllocationPaymentsInstance();
		},

		getAllocationPaymentsInstance() {
			return this.form.allocationPayments
				.map((allocationPayment) => allocationPayment.getData())
				.sort((a, b) => a.id - b.id);
		},

		openEditOnlyAllocationPayment() {
			this.sortAllocationPayments();
			if (this.editOnlyAllocationPayment !== null) {
				const index = this.form.allocationPayments.findIndex(
					(allocationPayment) => allocationPayment.id === this.editOnlyAllocationPayment.id
				);
				if (index >= 0) {
					const allocationPayment = this.form.allocationPayments[index];
					this.fetchAllocationPaymentComments(allocationPayment)
						.then((response) => {
							const commentsData = response.data;
							this.commentsConfiguration.comments = commentsData.comments;
							this.commentsConfiguration.tags = commentsData.tags;
							this.commentsConfiguration.token = commentsData.token;
							allocationPayment.setComments(commentsData.comments);
						})
						.finally(() => {
							this.modeState.send(this.modeState.Transitions.EDIT, index);
							this.backupAllocationPayment();
						});
				}
			}
		},

		fetchAllocationPaymentComments(allocationPayment) {
			return this.$http.get(
				'/' + this.routes['allocation-payment.comments.index'].replace('{allocationPayment}', allocationPayment.slug)
			);
		},

		fetchAllocationPayments() {
			return new Promise((resolve) => {
				this.modeState.send(this.modeState.Transitions.FETCH);
				this.$http
					.get('/' + this.routes['allocation-payment.allocation.index'].replace('{fundAllocation}', this.allocation.slug))
					.then((response) => {
						const allocationPayments = response.data;
						allocationPayments.forEach((allocationPayment) => {
							this.form.allocationPayments.push(new AllocationPaymentType(allocationPayment));
						});
					})
					.finally(() => {
						this.modeState.send(this.modeState.Transitions.EMPTY);
						resolve(this.form.allocationPayments);
					});
			});
		},

		getAllocationPaymentMaxAmount(exceptAllocationPaymentIndex) {
			let maxAmount =
				this.allocatedAmount -
				this.form.allocationPayments
					.filter((allocationPayment, allocationPaymentIndex) => exceptAllocationPaymentIndex !== allocationPaymentIndex)
					.filter((allocationPayment) => allocationPayment.status !== 'failed_permanently')
					.reduce((total, allocationPayment) => total + allocationPayment.amount, 0);

			return Math.abs(maxAmount);
		},

		keysListener(event) {
			if (event.code === 'Escape' && this.modeState.isEditMode()) {
				this.cancelAllocationPayment();
			}

			if (event.code === 'Enter' && this.modeState.isEditMode()) {
				this.saveAllocationPayment();
			}
		},

		formatAmount(amount) {
			return formatMoney(amount, this.locale, this.currency);
		},

		close() {
			this.allocatedAmount = null;
			this.allocationCreatedAt = null;
			this.grantEndDate = null;
			this.commentsConfiguration = this.getDefaultCommentConfiguration();
			this.modeState.resetState();
			this.modalState.send(this.modalState.Transitions.PAYMENTS);
			this.resetState({
				hasChanges: this.allocationPaymentsHaveChanges(),
			});
			this.currency = null;
			this.form.allocationPayments = [];
			this.form.backupAllocationPayments = [];
		},

		allocationPaymentsHaveChanges() {
			return JSON.stringify(this.getAllocationPaymentsInstance()) !== JSON.stringify(this.form.backupAllocationPayments);
		},

		getDefaultCommentConfiguration() {
			return {
				comments: [],
				tags: [],
				token: null,
			};
		},

		addAllocationPayment(data = {}, openAfterAppend = false) {
			data.allocation_id = this.allocation?.id;
			const allocationPayment = new AllocationPaymentType(data);
			const index = this.form.allocationPayments.push(allocationPayment);
			if (openAfterAppend) {
				this.modeState.send(this.modeState.Transitions.EDIT, index - 1);
			}

			this.sortAllocationPayments();
			this.focus();
		},

		focus() {
			this.$nextTick(() => {
				this.$refs.table.querySelector('tbody > tr:last-of-type > td:nth-child(2) > select')?.focus();
			});
		},

		sortAllocationPayments() {
			this.form.allocationPayments = this.form.allocationPayments.sort((a, b) => {
				const momentA = a.date_due ? moment(new Date(a.date_due)) : null;
				const momentB = b.date_due ? moment(new Date(b.date_due)) : null;

				if (momentA === null || momentB === null) {
					return 0;
				}

				if (momentA.isBefore(momentB)) {
					return -1;
				}

				if (momentA.isAfter(momentB)) {
					return 1;
				}

				return 0;
			});
		},

		selectPaymentScheduleTemplate(name, selected) {
			let paymentScheduleTemplate = this.mappedPaymentScheduleTemplates.find(
				(paymentScheduleTemplate) => paymentScheduleTemplate.slug === selected
			);

			if (!paymentScheduleTemplate) {
				return;
			}

			let allocationPayments = paymentScheduleTemplate.selectWithAmount(this.allocatedAmount);
			allocationPayments.forEach((allocationPayment) => this.addAllocationPayment(allocationPayment));
		},

		saveAllocationPayment() {
			return this.save(this.modeState.currentAllocationPaymentIndex, this.currentAllocationPayment);
		},

		save(index, allocationPayment) {
			this.resetErrors();
			this.modeState.send(this.modeState.Transitions.SAVE, index);
			return this.getSaveType(allocationPayment)
				.then((response) => {
					if (response?.data?.allocationPayment) {
						this.form.allocationPayments.splice(index, 1, new AllocationPaymentType(response.data.allocationPayment));
						this.modeState.send(this.modeState.Transitions.CLOSE);
						this.sortAllocationPayments();
					} else {
						this.modeState.send(this.modeState.Transitions.EDIT, index);
					}
				})
				.catch((error) => {
					this.modeState.send(this.modeState.Transitions.EDIT, index);
					if (error?.response?.status === 422) {
						this.validationErrors = error.response.data;
						this.error = this.lang.get('miscellaneous.alerts.validator.message').replace(/(<([^>]+)>)/gi, '');
					} else {
						tectoastr.error(this.lang.get('miscellaneous.alerts.generic'));
					}
				});
		},

		getSaveType(allocationPayment) {
			if (allocationPayment.hasDBRecord()) {
				return this.$http.put(
					'/' + this.routes['allocation-payment.update'].replace('{allocationPayment}', allocationPayment.slug),
					allocationPayment.getRequestParameters()
				);
			} else {
				return this.$http.post('/' + this.routes['allocation-payment.create'], allocationPayment.getRequestParameters());
			}
		},

		backupAllocationPayment() {
			let allocationPayment = this.currentAllocationPayment;
			if (allocationPayment) {
				this.modeState.backupAllocationPayment(allocationPayment.getInstance());
			}
		},

		restoreAllocationPayment() {
			if (this.modeState.isEditMode()) {
				const backup = this.modeState.currentAllocationPaymentBackup;
				if (backup) {
					this.form.allocationPayments.splice(this.modeState.currentAllocationPaymentIndex, 1, backup);
				}
			}
		},

		cancelAllocationPayment() {
			this.resetErrors();
			if (this.currentAllocationPayment?.canBeCancelledAndDelete()) {
				this.form.allocationPayments.splice(this.modeState.currentAllocationPaymentIndex, 1);
			} else {
				this.restoreAllocationPayment();
			}

			this.modeState.send(this.modeState.Transitions.EMPTY);
		},

		cancelCurrentAllocationPayment(index) {
			const allocationPayment = this.form.allocationPayments[index];

			if (allocationPayment?.canBeCancelledAndDelete()) {
				this.form.allocationPayments.splice(index, 1);
			} else {
				this.restoreAllocationPayment();
			}
		},

		commentAllocationPayment(allocationPayment, index) {
			this.resetErrors();
			this.cancelCurrentAllocationPayment(this.modeState.currentAllocationPaymentIndex);
			this.fetchAllocationPaymentComments(allocationPayment).then((response) => {
				const commentsData = response.data;
				this.commentsConfiguration.comments = commentsData.comments;
				this.commentsConfiguration.tags = commentsData.tags;
				this.commentsConfiguration.token = commentsData.token;
				this.modalState.send(this.modalState.Transitions.COMMENTS);
				this.modeState.send(this.modeState.Transitions.COMMENT, index);
			});
		},

		deleteAllocationPayment(index) {
			this.resetErrors();
			const allocationPayment = this.form.allocationPayments[index];

			this.modeState.send(this.modeState.Transitions.DELETE, index);
			if (allocationPayment.hasDBRecord()) {
				this.$http.delete('/' + this.routes['allocation-payment.delete'], {
					data: {
						selected: [allocationPayment.id],
					},
				});
			}

			this.modeState.send(this.modeState.Transitions.EMPTY);
			this.form.allocationPayments.splice(index, 1);
			this.sortAllocationPayments();
		},

		resetErrors() {
			this.error = null;
			this.validationErrors = null;
		},

		backToPayments() {
			this.modeState.send(this.modeState.Transitions.EMPTY);
			this.modalState.send(this.modalState.Transitions.PAYMENTS);
			this.commentsConfiguration = this.getDefaultCommentConfiguration();
		},

		doneClicked() {
			if (this.modeState.isFetching() || this.modeState.isSaving() || this.modeState.isDeleting()) {
				return;
			}

			if (this.isEditLockedToSinglePayment) {
				return this.close();
			}

			new Promise((resolve) => {
				this.resetErrors();
				this.form.allocationPayments.forEach((allocationPayment, index) => {
					this.modeState.send(this.modeState.Transitions.SAVE, index);
					if (allocationPayment.canBeSavedToDatabase()) {
						this.getSaveType(allocationPayment);
					}
				});
				resolve(true);
			}).then(() => {
				this.modeState.send(this.modeState.Transitions.EMPTY);
				this.close();
			});
		},

		commentAdded(comment, allComments) {
			this.currentAllocationPayment.setComments(allComments);
			this.commentsConfiguration.comments = allComments;
		},

		commentDeleted(comment, allComments) {
			this.currentAllocationPayment.setComments(allComments);
			this.commentsConfiguration.comments = allComments;
		},
	},
};
</script>

<style scoped lang="scss">
select::-ms-expand {
	display: none;
}

select {
	-webkit-appearance: none;
	-moz-appearance: none;
	background: transparent;
	background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
	background-repeat: no-repeat;
	background-position-x: 100%;
	background-position-y: 5px;
}

.allocated {
	text-align: right;
	margin-bottom: 7px;
	padding: 7px;
}

.allocation-payments-actions {
	border-top: 2px solid #ddd;
	padding: 7px;
	display: grid;
	grid-template-rows: repeat(3, 1fr);
	justify-items: flex-start;
	align-items: center;
}

@media (min-width: 992px) {
	.allocation-payments-actions {
		grid-template-columns: minmax(auto, 300px) min-content minmax(auto, 250px);
		grid-template-rows: 1fr;
		gap: 15px;
	}
}

.allocation-payments-table-headers {
	border-top: none;
	border-bottom: 2px solid #ddd;
}

.table-allocation-payments {
	tfoot {
		border-top: 2px solid #ddd;

		tr {
			&.statistic-line {
				td:nth-child(2) {
					width: 20%;
				}

				td:last-child {
					width: 16%;
				}
			}
		}
	}
}

.amount {
	white-space: nowrap;
}
</style>
