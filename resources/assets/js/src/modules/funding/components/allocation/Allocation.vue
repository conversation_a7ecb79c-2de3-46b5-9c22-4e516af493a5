<template>
	<tr class="funding-allocation">
		<td colspan="2" class="allocation-name">
			<div v-if="!isEditMode">
				<span v-text="value.name"></span>
				<div class="labels">
					<span v-output="displayTags"></span>
				</div>
			</div>
			<select-field
				v-if="isEditMode"
				name="fund"
				:items="funds"
				value-property="value"
				id-property="id"
				:value="value.fundId"
				:empty-option="false"
				:disabled="value.hasAllocationPayments()"
				@selected="(name, selected) => update('fundId', selected)"
			/>
		</td>
		<td class="text-right amount allocation-amount">
			<span v-if="isDisplayingLoader" class="af-icons af-icons-repeat af-icons-animate-rotate"></span>
			<allocation-payments-link
				v-else-if="modeState.isEmptyMode()"
				:label="value.formattedAmount"
				:currency="getCurrency"
				:allocation="value"
				:edit-only-allocation-payment="{}"
				@openModal="openModal"
			></allocation-payments-link>
			<!-- eslint-disable vue/no-mutating-props -->
			<currency-input
				v-else-if="isEditMode && isCurrentInEditMode"
				ref="amount"
				v-model="value.amount"
				class="text-right form-control"
				:distraction-free="distractionFree"
				:currency="null"
				:locale="locale"
				:precision="2"
				:allow-negative="false"
				:value-range="valueRange"
			/>
			<span v-else class="amount">{{ value.formattedAmount }}</span>
		</td>
		<td class="text-right amount allocation-paid">
			<span v-if="isDisplayingLoader" class="af-icons af-icons-repeat af-icons-animate-rotate"></span>
			<allocation-payments-link
				v-else-if="modeState.isEmptyMode()"
				:label="paidLabel"
				:currency="getCurrency"
				:allocation="value"
				:edit-only-allocation-payment="{}"
				@openModal="openModal"
			></allocation-payments-link>
			<span v-else class="amount">{{ paidLabel }}</span>
		</td>
		<td v-if="canDisplayActionOverFlow">
			<div class="dropdown action-overflow">
				<a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" role="button" tabindex="0">
					<i class="af-icons af-icons-action-overflow">
						<span class="sr-only">{{ lang.get('miscellaneous.perform_actions') }}</span>
					</i>
				</a>
				<ul class="dropdown-menu dropdown-menu-right">
					<li v-if="canDisplayEdit">
						<a href="#" class="allocation-edit ignore" @click.prevent="editAllocation" v-text="lang.get('buttons.edit')"></a>
					</li>
					<li v-if="allocationPermissions.canCreateDocument">
						<create-document
							name="create-document"
							method="POST"
							resource="allocation"
							:ids="[value.id]"
							:document-templates="documentTemplates"
							:document-template-translations="documentTemplateTranslations"
							:notifications="notifications"
							:file-types="fileTypes"
							:preferred-language="lang.locale"
							:labels="createDocumentLabels"
							:route="createDocumentRoute"
							@reveal="$emit('reveal', $event)"
						/>
					</li>
					<li>
						<allocation-payments-link
							v-if="canDisplayAllocationPayments"
							:label="lang.get('allocation-payments.buttons.payment_schedule') + '...'"
							:currency="getCurrency"
							:allocation="value"
							:edit-only-allocation-payment="{}"
							@openModal="openModal"
						></allocation-payments-link>
					</li>
					<li v-if="canDisplayDelete">
						<a
							href="#"
							class="allocation-delete ignore"
							@click.prevent="deleteAllocation"
							v-text="lang.get('buttons.delete')"
						></a>
					</li>
				</ul>
			</div>
		</td>
	</tr>
</template>

<script>
import AllocationType from './AllocationType';
import { SelectField } from 'vue-bootstrap';
import modeState, { states as modeStates } from './states/mode.state';
import { CurrencyInput } from 'vue-currency-input';
import AllocationPaymentsLink from '@/modules/allocation-payment/components/AllocationPaymentsLink.vue';
import { getGlobal } from '@/lib/utils.js';
import { tagLabels } from '@/common/tags';
import CreateDocument from '@/lib/components/ListActions/CreateDocument';

export default {
	name: 'Allocation',

	inject: ['lang'],

	components: {
		SelectField,
		CurrencyInput,
		CreateDocument,
		AllocationPaymentsLink,
	},

	props: {
		value: {
			type: AllocationType,
			default: () => new AllocationType(),
		},
		index: {
			type: Number,
			required: true,
		},
		allocationPermissions: {
			type: Object,
			default: () => {},
		},
		funds: {
			type: Array,
			default: () => [],
		},
		locale: {
			type: String,
			required: true,
		},
		documentTemplates: {
			type: Array,
			required: true,
		},
		documentTemplateTranslations: {
			type: [Object, Array],
			required: true,
		},
		notifications: {
			type: Array,
			required: true,
		},
		fileTypes: {
			type: Object,
			required: true,
		},
		createDocumentRoute: {
			type: String,
			required: true,
		},
		createDocumentLabels: {
			type: Object,
			default: () => ({}),
		},
		translations: {
			type: Object,
			default: () => {},
		},
		language: {
			type: String,
			default: getGlobal('language'),
		},
		defaultLanguage: {
			type: String,
			default: getGlobal('defaultLanguage'),
		},
	},

	data() {
		return {
			modeState,
		};
	},

	computed: {
		isDisplayingLoader() {
			return ['deleting', 'saving'].includes(this.modeState.currentState) && this.isCurrentInEditMode;
		},

		getCurrency() {
			return this.value.currency || this.funds.find((fund) => fund.id === this.value.fundId)?.currency;
		},

		paidLabel() {
			return this.value.paid ?? '—';
		},

		isEditMode() {
			return this.modeState.currentState === modeStates.edit && this.modeState.currentAllocationIndex === this.index;
		},

		isCurrentInEditMode() {
			return this.modeState.currentAllocationIndex === this.index;
		},

		valueRange() {
			return {
				min: 0,
				max: this.maxAmount,
			};
		},

		distractionFree() {
			return {
				hideCurrencySymbol: true,
				hideGroupingSymbol: false,
				hideNegligibleDecimalDigits: false,
			};
		},

		canDisplayAllocationPayments() {
			return (
				this.allocationPermissions.canViewPayments &&
				this.value.hasDBRecord() &&
				!(this.modeState.isSavingMode() || this.modeState.isDeletingMode())
			);
		},

		canDisplayDelete() {
			return (
				this.allocationPermissions.canDelete &&
				this.value.hasDBRecord() &&
				!(this.modeState.isSavingMode() || this.modeState.isDeletingMode())
			);
		},

		canDisplayEdit() {
			return (
				this.allocationPermissions.canUpdate &&
				!this.isEditMode &&
				!(this.modeState.isSavingMode() || this.modeState.isDeletingMode())
			);
		},

		canDisplayActionOverFlow() {
			if (!this.allocationPermissions.canUpdateOrDelete) {
				return false;
			}

			return this.canDisplayAllocationPayments || this.canDisplayDelete || this.canDisplayEdit;
		},
		displayTags() {
			return tagLabels(this.value.tags);
		},
	},

	methods: {
		update(key, value) {
			const allocation = this.value;
			allocation[key] = value;
			this.$emit('input', allocation);
		},

		editAllocation() {
			if (this.modeState.currentState === modeStates.edit) {
				this.$emit('close-open-forms');
			}

			this.modeState.send(this.modeState.Transitions.EDIT, this.index);
		},

		deleteAllocation() {
			this.$emit('delete');
		},

		openModal() {
			this.modeState.send(this.modeState.Transitions.PAYMENTS_SCHEDULE, this.index);
		},
	},
};
</script>

<style scoped>
.amount {
	white-space: nowrap;
}
</style>
