import { Collaborator } from '@/domain/models/Collaborator';
import { Consumer } from '@/domain/models/Consumer';
import { lockableServiceFactory } from '@/domain/services/Collaboration/Lockable';
import { useTimer } from '@/domain/utils/Timer';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { belongsToUser, DEADLINE, isLocked, Lock } from '@/domain/services/Collaboration/LockableLogic';
import { collaborationUIBus, lockSignalFor, unlockSignalFor } from '@/domain/signals/Collaboration';
import dataSources, { removeCacheDataSource } from '@/domain/services/Rt/DataSource';

vi.mock('@/domain/signals/Collaboration', () => ({
	collaborationUIBus: {
		on: vi.fn(),
		off: vi.fn(),
		emit: vi.fn(),
	},
	lockSignalFor: vi.fn(),
	unlockSignalFor: vi.fn(),
}));

vi.mock('underscore', () => ({
	default: {
		debounce: (fn: unknown) => fn,
	},
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		consumer: { slug: 'consumerSlug' } as Consumer,
	},
}));

vi.mock('@/domain/services/Collaboration/LockableLogic', () => ({
	isLocked: vi.fn(),
	belongsToUser: vi.fn(),
	DEADLINE: 1000,
}));

vi.mock('@/domain/services/Rt/DataSource', () => ({
	default: {
		locks: vi.fn(),
		collaborators: vi.fn(),
	},
	removeCacheDataSource: vi.fn(),
}));

vi.mock('@/domain/utils/Timer', () => ({
	useTimer: vi.fn(),
}));

describe('Field locking logic', () => {
	const service = lockableServiceFactory('submittableSlug', 'formSlug');

	const locks = {
		subscribe: vi.fn(),
		set: vi.fn(),
		destroy: vi.fn(),
		isReadonly: vi.fn(),
	};

	const makeTimer = () => ({
		start: vi.fn(),
		stop: vi.fn(),
	});

	const collaborators = {
		get: vi.fn(),
	};

	const ping = makeTimer();
	const refresh = makeTimer();

	const rebuildMocks = () => {
		vi.resetAllMocks();
		(lockSignalFor as Mock).mockReturnValue('lockSignal');
		(unlockSignalFor as Mock).mockReturnValue('unlockSignal');
		vi.spyOn(dataSources, 'locks').mockReturnValue(locks);
		vi.spyOn(dataSources, 'collaborators').mockReturnValue(collaborators);
		(useTimer as Mock).mockReturnValueOnce(ping).mockReturnValueOnce(refresh);
		vi.spyOn(Date.prototype, 'getTime').mockReturnValue(0);
	};

	beforeEach(() => {
		rebuildMocks();
	});

	it('should make a service for field', () => {
		const field = service('field-1');
		expect(dataSources.collaborators).toHaveBeenCalledWith('formSlug', 'submittableSlug');
		expect(dataSources.locks).toHaveBeenCalledWith('formSlug', 'submittableSlug', 'field-1', { slug: 'consumerSlug' });
		expect(belongsToUser).toHaveBeenCalledWith({ slug: 'consumerSlug' });
		expect(locks.subscribe).toHaveBeenCalledWith(expect.any(Function));

		rebuildMocks();
		service('field-2');
		expect(dataSources.locks).toHaveBeenCalledWith('formSlug', 'submittableSlug', 'field-2', { slug: 'consumerSlug' });
		expect(field).haveOwnProperty('subscribe');
		expect(field).haveOwnProperty('set');
	});

	it('sets up ping and refresh timers a value to locks datasource', () => {
		service('field-1');
		expect(useTimer).toHaveBeenCalledTimes(2);
	});

	it('locks and unlocks a lockable', () => {
		const fieldLock = { user: 'consumerSlug', deadline: DEADLINE } as Lock;
		service('field-1').set(true);
		expect(locks.set).toHaveBeenCalledWith(fieldLock, true);
		expect(refresh.start).toHaveBeenCalledOnce();
		expect(ping.stop).not.toHaveBeenCalled();

		rebuildMocks();
		service('field-1').set(false);
		expect(locks.set).toHaveBeenCalledWith(null, true);
		expect(refresh.stop).toHaveBeenCalledOnce();
		expect(refresh.start).not.toHaveBeenCalled();
	});

	it('unlocks field on another lock', () => {
		(useTimer as Mock).mockReturnValueOnce(ping).mockReturnValueOnce(refresh);

		const dataSourcesData = {
			'field-1': {
				subscribe: vi.fn(),
				set: vi.fn(),
				destroy: vi.fn(),
				isReadonly: vi.fn(),
			},
			'field-2': {
				subscribe: vi.fn(),
				set: vi.fn(),
				destroy: vi.fn(),
				isReadonly: vi.fn(),
			},
		};

		(dataSources.locks as Mock).mockImplementation(
			(formSlug, submittableSlug, lockableId) => dataSourcesData[lockableId]
		);

		const field1 = service('field-1');
		const field2 = service('field-2');

		field1.set(true);
		field2.set(true);

		expect(dataSourcesData['field-1'].set).toHaveBeenCalledWith({ user: 'consumerSlug', deadline: DEADLINE }, true);
		expect(dataSourcesData['field-2'].set).toHaveBeenCalledWith({ user: 'consumerSlug', deadline: DEADLINE }, true);
		expect(dataSourcesData['field-1'].set).toHaveBeenCalledWith(null, true);
	});

	it('emits signals, calls subscribers and stops ping on unlock', () => {
		let onLock: (fieldLock: Lock) => void = () => {};

		(locks.subscribe as Mock).mockImplementation((cb) => {
			onLock = cb as (fieldLock: Lock) => void;
		});

		const serviceInstance = service('field-1');

		const othersSubscriber = vi.fn();
		serviceInstance.subscribe(othersSubscriber);
		const mySubscriber = vi.fn();
		serviceInstance.subscribe(mySubscriber, true);

		(isLocked as Mock).mockReturnValue(false);
		onLock('lock' as unknown as Lock);

		expect(collaborationUIBus.emit).toHaveBeenCalledWith('unlockSignal', undefined);
		expect(othersSubscriber).toHaveBeenCalledWith(false);
		expect(mySubscriber).toHaveBeenCalledWith(false);
		expect(ping.stop).toHaveBeenCalledOnce();
		expect(ping.start).not.toHaveBeenCalled();
	});

	it('stops ping but does not emit signals / subscribers on lock refresh', () => {
		let onLock: (fieldLock: Lock) => void = () => {};

		(locks.subscribe as Mock).mockImplementation((cb) => {
			onLock = cb as (fieldLock: Lock) => void;
		});

		const isMine = vi.fn();
		(belongsToUser as Mock).mockReturnValue(isMine);

		const serviceInstance = service('field-1');

		const othersSubscriber = vi.fn();
		serviceInstance.subscribe(othersSubscriber);
		const mySubscriber = vi.fn();
		serviceInstance.subscribe(mySubscriber, true);

		(isLocked as Mock).mockReturnValue(true);
		isMine.mockReturnValue(true);
		onLock('lock' as unknown as Lock);

		expect(collaborationUIBus.emit).toHaveBeenCalled();
		expect(othersSubscriber).not.toHaveBeenCalled();
		expect(mySubscriber).toHaveBeenCalledWith(true);
		expect(ping.stop).toHaveBeenCalledOnce();
		expect(ping.start).not.toHaveBeenCalled();
	});

	it('restarts ping and emit signals / subscribers on others lock', async () => {
		let onLock: (fieldLock: Lock) => void = () => {};

		(locks.subscribe as Mock).mockImplementation((cb) => {
			onLock = cb as (fieldLock: Lock) => void;
		});

		vi.spyOn(collaborators, 'get').mockResolvedValue([{ user: 'foo' }, { user: 'bar' }]);

		const isMine = vi.fn();
		(belongsToUser as Mock).mockReturnValue(isMine);

		const serviceInstance = service('field-1');

		const othersSubscriber = vi.fn();
		serviceInstance.subscribe(othersSubscriber);
		const mySubscriber = vi.fn();
		serviceInstance.subscribe(mySubscriber, true);

		(isLocked as Mock).mockReturnValue(true);
		isMine.mockReturnValue(false);
		const locker = { user: 'bar' } as Collaborator;
		await onLock({ user: locker.user } as Lock);

		expect(collaborationUIBus.emit).toHaveBeenCalledWith('lockSignal', { locker });
		expect(othersSubscriber).toHaveBeenCalledWith(true, locker);
		expect(mySubscriber).not.toHaveBeenCalledWith();
		expect(ping.stop).toHaveBeenCalledOnce();
		expect(ping.start).toHaveBeenCalledOnce();
	});

	it('gets destroyed', () => {
		service('field-1').destroy();
		expect(locks.set).toHaveBeenCalledWith(null, true);
		expect(locks.destroy).toHaveBeenCalledOnce();
		expect(refresh.stop).toHaveBeenCalledOnce();
		expect(ping.stop).toHaveBeenCalledOnce();
		expect(collaborationUIBus.off).toHaveBeenCalledWith('lockSignal');
		expect(collaborationUIBus.off).toHaveBeenCalledWith('unlockSignal');
		expect(removeCacheDataSource).toHaveBeenCalledOnce();
	});
});
