import { Encrypter } from '@/domain/services/Encrypter';
import { User } from '@/domain/models/User';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { belongsToUser, isExpired, isLock } from '@/domain/services/Collaboration/LockableLogic';
import { guardForUser, LockableData, mutableDatSourceMapper } from '@/domain/services/Collaboration/MutableLogic';

vi.mock('@/domain/services/Encrypter', () => ({
	encrypter: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/LockableLogic', () => ({
	isLock: vi.fn(),
	isExpired: vi.fn(),
	belongsToUser: vi.fn(),
}));

describe('Mutable logic', () => {
	const encrypter = {
		encrypt: vi.fn(),
		decrypt: vi.fn(),
	} as unknown as Encrypter<unknown>;

	const guard = guardForUser('branca-danca' as unknown as User);

	beforeEach(() => {
		vi.resetAllMocks();
		(encrypter.encrypt as Mock).mockImplementation((value: string) => `encrypted: ${value}`);
		(encrypter.decrypt as Mock).mockImplementation((value: string) => `decrypted: ${value}`);
	});

	it('saves if there was no lock', () => {
		(isLock as unknown as Mock).mockReturnValue(false);
		const data = guard({ foo: 'bar', value: 'old-value' } as unknown as LockableData);

		expect(data).toBeTruthy();

		expect(isLock).toHaveBeenCalledOnce();
		expect(belongsToUser).not.toHaveBeenCalled();
		expect(isExpired).not.toHaveBeenCalled();
	});

	it('saves if there is lock but its mine', () => {
		(isLock as unknown as Mock).mockReturnValue(true);
		(belongsToUser as unknown as Mock).mockReturnValue(() => true);

		const data = guard({ foo: 'bar', value: 'old-value' } as unknown as LockableData);
		expect(data).toBeTruthy();

		expect(isLock).toHaveBeenCalledOnce();
		expect(belongsToUser).toHaveBeenCalledWith('branca-danca');
		expect(isExpired).not.toHaveBeenCalled();
	});

	it('saves if there is lock and its not mine but its expired', () => {
		(isLock as unknown as Mock).mockReturnValue(true);
		(belongsToUser as unknown as Mock).mockReturnValue(() => false);
		(isExpired as unknown as Mock).mockReturnValue(true);

		const data = guard({ foo: 'bar', value: 'old-value' } as unknown as LockableData);
		expect(data).toBeTruthy();

		expect(isLock).toHaveBeenCalledOnce();
		expect(belongsToUser).toHaveBeenCalledWith('branca-danca');
		expect(isExpired).toHaveBeenCalledOnce();
	});

	it('does not save if there is lock and its not mine and its not expired', () => {
		(isLock as unknown as Mock).mockReturnValue(true);
		(belongsToUser as unknown as Mock).mockReturnValue(() => false);
		(isExpired as unknown as Mock).mockReturnValue(false);

		const data = guard({ foo: 'bar', value: 'old-value' } as unknown as LockableData);
		expect(data).toBeFalsy();

		expect(isLock).toHaveBeenCalledOnce();
		expect(belongsToUser).toHaveBeenCalledWith('branca-danca');
		expect(isExpired).toHaveBeenCalledOnce();
	});

	it('uses data mapper', () => {
		const mapper = mutableDatSourceMapper('branca-danca' as unknown as User);
		expect(mapper.rawProps).toEqual(['lock']);
		expect(mapper.guard).instanceOf(Function);
		expect(mapper.extractData({ value: 'value', lock: 'lock' })).toBe('value');
		expect(mapper.prepareData({ value: 'value', lock: 'lock' }, 'new-value')).toEqual({
			value: 'new-value',
			lock: 'lock',
		});
	});
});
