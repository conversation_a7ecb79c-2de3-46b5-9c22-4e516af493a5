import { User } from '@/domain/models/User';
import { belongsToUser, isLocked, Lock, lockableDatSourceMapper } from '@/domain/services/Collaboration/LockableLogic';
import { describe, expect, it } from 'vitest';

describe('Field locking logic', () => {
	const myself = { slug: 'myself' } as User;
	const other = { slug: 'other' } as User;

	const isMine = belongsToUser(myself);

	it('shows unlocked if there is no record', () => {
		expect(isLocked()).toBe(false);
		expect(isLocked(null)).toBe(false);
	});

	it('shows unlocked if its too old', () => {
		const data = {
			user: other.slug,
			deadline: 0,
		} as Lock;

		expect(isLocked(data)).toBe(false);
	});

	it('shows its mine if its mine', () => {
		const data = {
			user: myself.slug,
			deadline: new Date().getTime() + 1000,
		} as Lock;

		expect(isMine(data)).toBe(true);
	});

	it('shows its not mine if its null or expired', () => {
		const data = {
			user: other.slug,
			deadline: new Date().getTime() - 1000,
		} as Lock;

		expect(isMine(data)).toBe(false);
		expect(isMine(null)).toBe(false);
	});

	it('shows its not mine if its not mine', () => {
		const data = {
			user: 'foo-bar',
			deadline: new Date().getTime() + 1000,
		} as Lock;

		expect(isMine(data)).toBe(false);
	});

	it('uses data mapper', () => {
		const mapper = lockableDatSourceMapper('branca-danca' as unknown as User);
		expect(mapper.rawProps).toEqual(['lock']);
		expect(mapper.guard).instanceOf(Function);
		expect(mapper.extractData({ value: 1, lock: 'foo' })).toBe('foo');
		expect(mapper.prepareData({ value: 1, lock: 'foo' }, 'bar')).toEqual({ value: 1, lock: 'bar' });
	});

	it('overrides the value.sender data with the locker user slug', () => {
		expect(
			lockableDatSourceMapper('branca-danca' as unknown as User).prepareData(
				{ value: { value: 1, sender: 'johnslug' } },
				{ deadline: 123123, user: 'userlug' }
			)
		).toEqual({ value: { value: 1, sender: 'userlug' }, lock: { deadline: 123123, user: 'userlug' } });
	});
});
