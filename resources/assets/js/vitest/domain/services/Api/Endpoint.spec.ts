import { RoutesGetter } from '@/domain/dao/Routes';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DataResponse, dataSource } from '@/domain/services/Api/DataSource';
import { DeletingEndpoint, GettingEndpoint, PostingEndpoint, PuttingEndpoint } from '@/domain/services/Api/Endpoint';

vi.mock('@/domain/services/Api/DataSource', async () => {
	const actual: object = await vi.importActual('@/domain/services/Api/DataSource');
	return {
		...actual,
		dataSource: {
			request: vi.fn(),
		},
	};
});

vi.mock('@/domain/services/Api/Headers', () => ({
	headersFactory: (headers: Record<string, string> = {}) => ({ ...headers, csrfToken: 'csrfToken-value' }),
}));

const headers = {
	'X-Test': 'test-value',
};

const requestExpectation = (method: string, url: string, data?: unknown) => ({
	url,
	method,
	data,
	headers: { ...headers, csrfToken: 'csrfToken-value' },
});

describe('API', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test' } as DataResponse<any>);
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('sends GET requests', () => {
		GettingEndpoint('test-get', headers)();
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('get', 'test-get'));
	});

	it('sends GET requests with route getter', () => {
		const routeGetter = (p: string) => `test-get-${p}`;
		GettingEndpoint(routeGetter as RoutesGetter, headers)('abc');
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('get', 'test-get-abc'));
	});

	it('sends DELETE requests', () => {
		DeletingEndpoint('test-delete', headers)();
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('delete', 'test-delete'));
	});

	it('sends DELETE requests with route getter', () => {
		const routeGetter = (p: string) => `test-delete-${p}`;
		DeletingEndpoint(routeGetter as RoutesGetter, headers)('abc');
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('delete', 'test-delete-abc'));
	});

	it('sends POST requests', () => {
		PostingEndpoint('test-post', headers)()({ test: 'test' });
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('post', 'test-post', { test: 'test' }));
	});

	it('sends POST requests with route getter', () => {
		const routeGetter = (p: string) => `test-post-${p}`;
		PostingEndpoint(routeGetter as RoutesGetter, headers)('abc')({ test: 'test' });
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('post', 'test-post-abc', { test: 'test' }));
	});

	it('sends PUT requests', () => {
		PuttingEndpoint('test-put', headers)()({ test: 'test' });
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('put', 'test-put', { test: 'test' }));
	});

	it('sends PUT requests with route getter', () => {
		const routeGetter = (p: string) => `test-put-${p}`;
		PuttingEndpoint(routeGetter as RoutesGetter, headers)('abc')({ test: 'test' });
		expect(dataSource.request).toHaveBeenCalledWith(requestExpectation('put', 'test-put-abc', { test: 'test' }));
	});
});
