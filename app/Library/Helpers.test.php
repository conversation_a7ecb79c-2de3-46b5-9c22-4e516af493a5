<?php

namespace AwardForce\Library;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Facades\CurrentLocale;
use AwardForce\Library\PaymentSubscriptions\Contracts\PaymentSubscriptionGateway;
use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Accounts\Models\SupportedLanguage;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Comments\Models\CommentCollection;
use AwardForce\Modules\Comments\Tags\StringTag;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Menu\Services\ContextService;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Enums\LocalIdShortcodeFormat;
use AwardForce\Modules\Settings\Models\Setting;
use Carbon\Carbon;
use Eloquence\Behaviours\Slug;
use Faker\Factory;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\View\View;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Mockery as m;
use NumberFormatter;
use PHPUnit\Framework\Attributes\TestWith;
use Platform\Categories\CategoryListGenerator;
use Platform\Database\Eloquent\Collection as PlatformCollection;
use Platform\Kessel\Hyperdrive;
use Platform\Language\Language;
use Platform\Localisation\Translation;
use Platform\Menu\Context\Items\Guest;
use Ramsey\Uuid\Uuid;
use Symfony\Component\VarDumper\VarDumper;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;
use Tests\MockConsumer;
use Tests\Search\Filters\Entity;
use Tests\Swaps\CurrentAccountService;

final class HelpersTest extends BaseTestCase
{
    use Laravel;

    protected $settings;
    protected $feature;

    public function init()
    {
        $account = new Account;
        $account->id = 123;
        CurrentAccount::swap(new CurrentAccountService($account));
        $this->account = $account;
    }

    public function testImgixWithExpirySpecified(): void
    {
        Feature::spy()->shouldReceive('enabled')->andReturnTrue()->byDefault()
            ->shouldReceive('enabled')->with('image_optimisation')->andReturnFalse();

        $this->mock('overload:Aws\CloudFront\CloudFrontClient')
            ->shouldReceive('getSignedUrl')
            ->once()
            ->andReturn($tenYears = time() + 10 * 365 * 24 * 60 * 60);

        $resolvedSpecifiedTimeExpected = imgix('test', params: ['expiry' => 10 * 365 * 24 * 60 * 60]); // 10 year in minutes
        $this->assertGreaterThanOrEqual($tenYears, $resolvedSpecifiedTimeExpected);
    }

    public function testImgixDefaultingToAwsCloudfrontWithSpecificExpiryTime()
    {
        Feature::spy()->shouldReceive('enabled')->andReturnTrue()->byDefault()
            ->shouldReceive('enabled')->with('image_optimisation')->andReturnFalse();

        $this->mock('overload:Aws\CloudFront\CloudFrontClient')
            ->shouldReceive('getSignedUrl')
            ->once()
            ->andReturn($default = time() + 60 * 60);

        $resolvedNoSpecifiedTimeExpected = imgix('test'); // by default should be 1 hour
        $this->assertGreaterThanOrEqual($default, $resolvedNoSpecifiedTimeExpected);

        $resolvedNullSpecifiedTimeExpected = imgix('test', params: ['expiry' => null]); //defaulting to 1 hour
        $this->assertGreaterThanOrEqual($default, $resolvedNullSpecifiedTimeExpected);
    }

    public function testToKb(): void
    {
        $this->assertSame(1229, to_kb(1.2));
        $this->assertSame(12493, to_kb(12.2));
    }

    public function testFileExtension(): void
    {
        $this->assertEquals('jpg', file_extension('filename.jpg'));
        $this->assertEquals('png', file_extension('filename.png'));
        $this->assertEquals('tar.gz', file_extension('filename.tar.gz'));
        $this->assertEquals('zip', file_extension('filename.zip'));
        $this->assertEquals('pdf', file_extension('filename.pdf'));
        $this->assertEquals('tar', file_extension('filename.tar'));
        $this->assertEquals('gz', file_extension('filename.gz'));
    }

    public function testLocalIdWithoutCategory(): void
    {
        $entry = m::mock(Entry::class)->makePartial();
        $entry->localId = 7564;
        $entry->shouldReceive('getAttribute')->with('category')->andReturn(null);

        $this->assertEquals($entry->localId, local_id($entry));
    }

    public function testStartWith()
    {
        $this->assertEquals(prefix('abcdefg', 'ab'), 'abcdefg');
        $this->assertEquals(prefix('bcdefg', 'a'), 'abcdefg');
    }

    public function testFileUrl(): void
    {
        $file1 = new File;
        $file1->file = 'some-image.img';

        $file2 = new File;
        $file2->file = '/leading-slash.html';

        $file3 = new File;
        $file3->file = '///too-many-slashes.png';

        $storage = config('filesystems.disks.'.config('filesystems.default').'.bucket_url');

        $this->assertSame($storage.'some-image.img', file_url($file1));
        $this->assertSame($storage.'leading-slash.html', file_url($file2));
        $this->assertSame($storage.'too-many-slashes.png', file_url($file3));
    }

    public function testMergeGroupConcat(): void
    {
        $original = [
            '1,2,3',
            '2,3,4',
            '',
            '7,9',
            '1,',
        ];

        $expected = [1, 2, 3, 4, 7, 9];

        $this->assertEquals($expected, merge_group_concat($original));
    }

    public function testMergeGroupConcatNested(): void
    {
        $original = [
            [1, 2, 3],
            [2, 3, 4],
            '',
            '7,9',
            [1],
        ];

        $expected = [1, 2, 3, 4, 7, 9];

        $this->assertEquals($expected, merge_group_concat($original));
    }

    public function testMergeGroupConcatCollection(): void
    {
        $original = [
            [1, 2, 3],
            [2, 3, 4],
            '',
            '7,9',
            [1],
        ];

        $expected = new Collection([1, 2, 3, 4, 7, 9]);

        $this->assertEquals($expected, merge_group_concat($original, true));
    }

    public function testStripSpecialChars(): void
    {
        $strings = [
            'ABC123*()',
            'ABC123_-_',
            'ABC 123',
            'Bastards < > : " / \ | ? * incorporated Entry 061',
            'Νοτ αν ασcιι τεξτ',
            'Grant+reports!@#$%^&*()_-={}[]|;’:",./<>?`~',
        ];

        $expected = [
            'ABC123',
            'ABC123_-_',
            'ABC 123',
            'Bastards incorporated Entry 061',
            'Not an ascii tekst',
            'Grantreports_-',
        ];

        $actual = [];

        foreach ($strings as $string) {
            $actual[] = strip_special_chars($string);
        }

        $this->assertSame($expected, $actual);

        $actual = [];
        foreach ($strings as $string) {
            $actual[] = strip_special_chars($string, false);
        }

        $this->assertSame($strings[4], $actual[4]);
    }

    public function testAmountFormatting(): void
    {
        // Set up the translator to mock out the currency calls below
        $this->mockTranslations();

        $amount = new Amount(87348734.83, new Currency('USD'));

        $this->assertEquals('$ 87,348,734.83', format_amount($amount));
    }

    public function testNegativeAmount(): void
    {
        // Set up the translator to mock out the currency calls below
        $this->mockTranslations();

        $amount = new Amount(1250.45, new Currency('USD'));

        $this->assertEquals('$ -1,250.45', format_amount($amount, ' ', true));
    }

    public function testThumbnailSrcReturnsUrlForAValidUrl(): void
    {
        $urlThumbnail = 'https://example.com/image.jpg';
        $resultImage = thumbnail_src($urlThumbnail, ['w' => 200, 'h' => 200]);

        $this->assertEquals($urlThumbnail, $resultImage);
    }

    public function testThumbnailSrcReturnsImgixForAFilePath(): void
    {
        Feature::shouldReceive('enabled')->with('image_optimisation')->andReturn(true);
        CurrentAccount::shouldReceive('attribute')->with('region')->andReturn('eu');
        $domain = config('services.imgix.eu')['domain'];
        $path = 'path/to/image.jpg';

        $resultUrl = thumbnail_src($path, ['w' => 200, 'h' => 200]);

        $this->assertStringStartsWith("http://$domain/$path", $resultUrl);
    }

    public function testGetYoutubeID(): void
    {
        $expected = 'nCwRJUg3tcQ';

        $urls = [
            'http://www.youtube.com/watch?v=nCwRJUg3tcQ&feature=g-all-f&context=G27f364eFAAAAAAAAAAA',
            'http://www.youtube.com/watch?feature=g-all-f&v=nCwRJUg3tcQ&context=G27f364eFAAAAAAAAAAA',
            '<iframe width="560" height="315" src="http://www.youtube.com/embed/nCwRJUg3tcQ" frameborder="0" allowfullscreen></iframe>',
            'youtube.com/v/nCwRJUg3tcQ',
            'youtube.com/vi/nCwRJUg3tcQ',
            'youtube.com/?v=nCwRJUg3tcQ',
            'youtube.com/?vi=nCwRJUg3tcQ',
            'youtube.com/watch?v=nCwRJUg3tcQ',
            'youtube.com/watch?vi=nCwRJUg3tcQ',
            'youtu.be/nCwRJUg3tcQ',
            'https://www.youtube.com/watch?v=nCwRJUg3tcQ&list=PLv5BUbwWA5RYaM6E-QiE8WxoKwyBnozV2&index=4',
            'https://youtube.com/v/nCwRJUg3tcQ',
            'https://youtube.com/vi/nCwRJUg3tcQ',
            'https://youtube.com/?v=nCwRJUg3tcQ',
            'https://youtube.com/?vi=nCwRJUg3tcQ',
            'https://youtube.com/watch?v=nCwRJUg3tcQ',
            'https://youtube.com/watch?vi=nCwRJUg3tcQ',
            'https://youtu.be/nCwRJUg3tcQ',
            'https://youtube.com/v/nCwRJUg3tcQ',
            'https://youtube.com/vi/nCwRJUg3tcQ',
            'https://youtube.com/?v=nCwRJUg3tcQ',
            'https://youtube.com/?vi=nCwRJUg3tcQ',
            'https://youtube.com/watch?v=nCwRJUg3tcQ',
            'https://youtube.com/watch?vi=nCwRJUg3tcQ',
            'https://youtu.be/nCwRJUg3tcQ',
            'https://youtube.com/embed/nCwRJUg3tcQ',
            'http://youtube.com/v/nCwRJUg3tcQ',
            'http://www.youtube.com/v/nCwRJUg3tcQ',
            'https://www.youtube.com/v/nCwRJUg3tcQ',
        ];

        foreach ($urls as $url) {
            $this->assertSame($expected, get_youtube_id($url));
        }
    }

    public function testUrlShouldNotReturnYoutubeId(): void
    {
        $this->assertFalse(get_youtube_id('https://www.mini.nl/nl_NL/home/<USER>/movies/portfolio.html'));
    }

    public function testGetVimeoID(): void
    {
        $expected = '87973054';

        $urls = [
            'https://vimeo.com/87973054',
            'http://vimeo.com/87973054',
            'http://vimeo.com/87973054',
            'http://player.vimeo.com/video/87973054?title=0&amp;byline=0&amp;portrait=0',
            'http://player.vimeo.com/video/87973054',
            'http://player.vimeo.com/video/87973054',
            'http://player.vimeo.com/video/87973054?title=0&amp;byline=0&amp;portrait=0',
            'http://vimeo.com/channels/vimeogirls/87973054',
            'http://vimeo.com/channels/vimeogirls/87973054',
            'http://vimeo.com/channels/staffpicks/87973054',
            'http://vimeo.com/87973054',
            'http://vimeo.com/channels/vimeogirls/87973054',
        ];

        foreach ($urls as $url) {
            $this->assertSame($expected, get_vimeo_id($url));
        }
    }

    public function testGetTiktokId(): void
    {
        $expected = '7228218086191680773';

        $urls = [
            'https://www.tiktok.com/@creativeforce/video/7228218086191680773?is_from_webapp=1&sender_device=pc&web_id=7226880921865356801',
            'https://www.tiktok.com/@creativeforce/video/7228218086191680773',
        ];

        foreach ($urls as $url) {
            $this->assertSame($expected, get_tiktok_id($url));
        }
    }

    public function testGetTwitchId(): void
    {
        // Test with an invalid URL
        $url = 'https://www.google.com';
        $this->assertFalse(get_twitch_id($url));

        // Test with a valid Twitch URL
        $expected = 'channelname';

        $urls = [
            'https://www.twitch.tv/channelname',
            'https://twitch.tv/channelname',
            '<iframe src="https://player.twitch.tv/?channel=channelname&parent=www.example.com" frameborder="0" allowfullscreen="true" scrolling="no" height="378" width="620"></iframe>',
        ];

        foreach ($urls as $url) {
            $this->assertSame($expected, get_twitch_id($url));
        }
    }

    public function testGetInstagramId(): void
    {
        // Test with an invalid URL
        $url = 'https://www.google.com';
        $this->assertFalse(get_instagram_id($url));

        // Test with a valid Twitch URL
        $expected = 'C0tpkEwOQne';

        $urls = [
            'https://www.instagram.com/reel/C0tpkEwOQne/',
            'https://www.instagram.com/reel/C0tpkEwOQne/embed',
            'https://www.instagram.com/reel/C0tpkEwOQne/?utm_source=ig_web_copy_link',
            '<blockquote class="instagram-media" data-instgrm-captioned data-instgrm-permalink="https://www.instagram.com/reel/C0tpkEwOQne/?utm_source=ig_embed&amp;utm_campaign=loading" data-instgrm-version="14"><div><a href="https://www.instagram.com/reel/C0tpkEwOQne/?utm_source=ig_embed&amp;utm_campaign=loading" target="_blank"><div><div>View this post on Instagram</div></div><div></a><p><a href="https://www.instagram.com/reel/C0tpkEwOQne/?utm_source=ig_embed&amp;utm_campaign=loading" target="_blank">A post shared by Google (@google)</a></p></div></blockquote> <script async src="//www.instagram.com/embed.js"></script>',
        ];

        foreach ($urls as $url) {
            $this->assertSame($expected, get_instagram_id($url));
        }
    }

    public function testCloudAssetUrlDirectToS3(): void
    {
        CurrentAccount::shouldReceive('attribute')->with('region')->andReturn('au');

        Config::set('filesystems.disks.s3-au.bucket_url', 'https://s3-somewhere.amazonaws.com/my-bucket/');
        Config::set('services.aws.cloudfront.au.domain', null);

        $this->assertEquals(
            'https://s3-somewhere.amazonaws.com/my-bucket/files/my-file.jpg',
            cloud_asset_url('files/my-file.jpg')
        );

        Config::set('services.aws.cloudfront.au.domain', 'au');
        CurrentAccount::shouldReceive('get')->with('region')->andReturn('au');
    }

    public function testFilenameIsEncodedCorrectlyAndItAddsResponseContentDispositionURLParam(): void
    {
        CurrentAccount::shouldReceive('attribute')->with('region')->once()->andReturn('au');
        Config::set('services.aws.cloudfront.au.domain', 'myhouse.com');
        Config::set('services.aws.cloudfront.au.private_key_path', 'path');
        Config::set('services.aws.cloudfront.au.key_pair_id', 'key_id');
        $this->freezeTime();

        $this->mock('overload:Aws\CloudFront\CloudFrontClient')
            ->shouldReceive('getSignedUrl')
            ->once()
            ->with([
                'url' => 'https://myhouse.com/files/my-file.jpg?response-content-disposition=filename%2A%3DUTF-8%27%27%25D8%25B2%25D9%2587%25D8%25B1%25D8%25A9.png',
                'expires' => now()->addMinutes(60)->timestamp,
                'private_key' => 'path',
                'key_pair_id' => 'key_id',
            ])
            ->andReturn('https://myhouse.com/files/my-file.jpg?response-content-disposition=filename%2A%3DUTF-8%27%27%25D8%25B2%25D9%2587%25D8%25B1%25D8%25A9.png');

        cloud_asset_url('files/my-file.jpg', filename: 'زهرة.png');
    }

    public function testCloudAssetUrlViaCloudfront(): void
    {
        if (! config('services.aws.cloudfront.au.domain')) {
            $this->markTestSkipped('CloudFront not configured.');
        }

        CurrentAccount::shouldReceive('attribute')->with('region')->andReturn('au');

        Config::set('services.aws.cloudfront.au.domain', 'myhouse.com');

        $this->assertStringStartsWith('https://myhouse.com/my-file.png', cloud_asset_url('my-file.png'));
    }

    public function testPreviewScoreSetJudgeComments(): void
    {
        // Fix for GlobalComposer include on template
        $this->mockConsumer();

        $comments = m::mock(CommentCollection::class);
        $comment = m::mock('comment');
        $comment->shouldReceive('hasTag')->andReturn(false);
        $comments->shouldReceive('groupByTypes')->once()->andReturn(collect(['judging' => collect(), 'conflict' => collect(), 'abstention' => collect()]));

        $comment->comment = 'comment';
        $comment->createdAt = Carbon::now();
        $comment->updatedAt = Carbon::now();

        $file1 = new File;
        $file1->file = 'files/7/y/1/l/Z/k/LKXTt2HapO/file.docx';
        $file1->mime = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        $file1->size = 1234;

        $file2 = new File;
        $file2->file = 'files/7/y/1/l/Z/k/aBcDeFgHiJ/photo.jpg';
        $file2->mime = 'image/jpeg';
        $file2->size = 123;

        $comment->files = collect([$file1, $file2]);

        $scoreSet = new ScoreSet;
        $scoreSet->id = 1;

        $judge = new User([
            'id' => 2,
            'name' => 'judge name',
        ]);

        $criterion = new ScoringCriterion;
        $criterion->id = 3;

        $criteria = new PlatformCollection([new ScoringCriterion()]);
        preview_score_set_judge_comments($comments, $scoreSet, $judge, $criteria);
    }

    public function testIsImage(): void
    {
        $image1 = 'image.jpg';
        $image2 = 'image.png';
        $image3 = 'image.gif';

        $audio = 'audio.mp3';
        $video = 'video.mpeg';
        $doc = 'document.pdf';

        $this->assertTrue(is_image($image1));
        $this->assertTrue(is_image($image2));
        $this->assertTrue(is_image($image3));

        $this->assertFalse(is_image($audio));
        $this->assertFalse(is_image($video));
        $this->assertFalse(is_image($doc));
    }

    public function testIsAudio(): void
    {
        $audio1 = 'audio.mp3';
        $audio2 = 'audio.m4a';
        $audio3 = 'audio.wav';

        $image = 'image.jpg';
        $video = 'video.mpeg';
        $doc = 'document.pdf';

        $this->assertTrue(is_audio($audio1));
        $this->assertTrue(is_audio($audio2));
        $this->assertTrue(is_audio($audio3));

        $this->assertFalse(is_audio($image));
        $this->assertFalse(is_audio($video));
        $this->assertFalse(is_audio($doc));
    }

    public function test_protect_value(): void
    {
        $this->assertEquals('*****', protect_value('abcde', 0));
        $this->assertEquals('***de', protect_value('abcde', 2));
    }

    public function testIsRtl(): void
    {
        CurrentLocale::shouldReceive('code')->andReturn('ar_AR');

        $this->assertTrue(is_rtl());
        $this->assertSame('rtl', ltr_rtl_dir());
        $this->assertSame('right;', ltr_rtl_align());
    }

    public function testIsLtr(): void
    {
        CurrentLocale::shouldReceive('code')->andReturn('en_GB');

        $this->assertFalse(is_rtl());
        $this->assertSame('ltr', ltr_rtl_dir());
        $this->assertSame('left;', ltr_rtl_align());
    }

    public function testRtlHelpers(): void
    {
        CurrentLocale::shouldReceive('code')->once()->andReturn('ar_AR');

        $this->assertTrue(is_rtl());

        CurrentLocale::shouldReceive('code')->once()->andReturn('en_GB');

        $this->assertFalse(is_rtl());
    }

    public function testScoreFormat(): void
    {
        $this->assertEquals('0', score_format('0'));
        $this->assertEquals('100', score_format(100));
        $this->assertEquals('100.1', score_format('100.1'));
        $this->assertEquals('100.12', score_format(100.12));
        $this->assertEquals('100.12', score_format('100.123'));
        $this->assertEquals('100.13', score_format(100.126));
    }

    public function testFirstOrMax(): void
    {
        $this->assertEquals(100, first_or_max(100, 10));
        $this->assertEquals(100, first_or_max(10, 100));
        $this->assertEquals(100, first_or_max(100, 0));
        $this->assertEquals(0, first_or_max(0, 100));
    }

    public function testFirstOrMin(): void
    {
        $this->assertEquals(10, first_or_min(100, 10));
        $this->assertEquals(10, first_or_min(10, 100));
        $this->assertEquals(10, first_or_min(10, 0));
        $this->assertEquals(0, first_or_min(0, 10));
    }

    public function testPaginationSinglePageItem(): void
    {
        // Paginator, 20 items, 5 per page, on page 2
        $paginator = new LengthAwarePaginator([], 20, 5, 2);

        // Assert second item is page 7
        $this->assertEquals(7, pagination_single_page_item($paginator, 1));
    }

    public function testNextPreviousToken(): void
    {
        // Paginator, 20 items, 5 per page, on page 2
        $paginator = new LengthAwarePaginator([], 20, 5, 2);

        // Assert second item is page 7
        $this->assertEquals('token-7', next_previous_token($paginator, 1, 'token'));
    }

    public function testScoreSetUrl(): void
    {
        $scoreSet = new ScoreSet;
        $scoreSet->slug = new Slug('slug');

        $scoreSet->mode = ScoreSet::MODE_GALLERY;
        $this->assertStringEndsWith('gallery/slug', score_set_url($scoreSet));

        $scoreSet->mode = ScoreSet::MODE_QUALIFYING;
        $this->assertStringEndsWith('qualify?score-set=slug', score_set_url($scoreSet));

        $scoreSet->mode = ScoreSet::MODE_TOP_PICK;
        $this->assertStringEndsWith('pick?score-set=slug', score_set_url($scoreSet));

        $scoreSet->mode = ScoreSet::MODE_VOTING;
        $this->assertStringEndsWith('vote/slug', score_set_url($scoreSet));

        $scoreSet->mode = ScoreSet::MODE_VIP;
        $this->assertStringEndsWith('entry/judge', score_set_url($scoreSet));
    }

    public function testScoreSetUrlDeletedOrNull(): void
    {
        $scoreSet = new ScoreSet;

        $scoreSet->deletedAt = Carbon::now();
        $this->assertEmpty(score_set_url($scoreSet));

        $this->assertEmpty(score_set_url(null));
    }

    public function testSeededShuffle(): void
    {
        $original = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        $seed = random_int(0, 10000);

        // Different seeds should not match
        $this->assertNotEquals(seeded_shuffle($original, $seed), seeded_shuffle($original, $seed + 1));

        // Ensure two subsequent executions still match
        $this->assertEquals(seeded_shuffle($original, $seed), seeded_shuffle($original, $seed));
    }

    public function testStrArrayToDotNotation(): void
    {
        $this->assertEquals('values.ypEwboGD.oPsmdaDa', str_array_to_dot_notation('values[ypEwboGD][oPsmdaDa]'));
    }

    public function testExternalUrlSession(): void
    {
        $redirect = external_url($raw = 'http://some.site/page?query=param');
        $token = substr($redirect, strrpos($redirect, '/') + 1);

        $this->assertNotEquals($raw, $redirect);
        $this->assertEquals($raw, Session::get('redirect-external:'.$token));
    }

    public function testExternalUrlCache(): void
    {
        $redirect = external_url($raw = 'http://some.site/page?query=param', 5);
        $token = substr($redirect, strrpos($redirect, '/') + 1);

        $this->assertNotEquals($raw, $redirect);
        $this->assertEquals($raw, Cache::get('redirect-external:'.$token));
    }

    public function testForVue(): void
    {
        $entryA = new Entry;
        $entryA->id = 2;
        $entryA->title = 'A';

        $entryZ = new Entry();
        $entryZ->id = 1;
        $entryZ->title = 'Z';

        $collection = collect([$entryZ, $entryA]);

        $this->assertEquals(
            [['id' => 2, 'title' => 'A'], ['id' => 1, 'title' => 'Z']],
            for_vue($collection, ['id', 'title'], 'title')
        );
    }

    public function testAppAssetUrl(): void
    {
        Config::set('app.version', null);

        $url1 = app_asset_url('robots.txt');

        Config::set('app.version', $commit = 'b64f49553d5c441652e95697a2c5949e');
        Config::set('services.aws.cloudfront.assets.domain', '98273498723497234.cloudfront.com');

        $url2 = app_asset_url('robots.txt');
        $url3 = app_asset_url('awardforce.min.css');

        $this->assertSame('/robots.txt', $url1);
        $this->assertSame('//98273498723497234.cloudfront.com/assets/'.$commit.'/robots.txt', $url2);
        $this->assertSame('//98273498723497234.cloudfront.com/assets/'.$commit.'/awardforce.min.css', $url3);
    }

    public function testGitCommit(): void
    {
        $file = base_path('.git_commit_nr');
        @unlink($file);

        $this->assertNull(git_commit());

        file_put_contents($file, 'test');

        $this->assertEquals('test', git_commit());

        @unlink($file);
    }

    public function testRegionQueueName(): void
    {
        Config::set('queue.regions.eu.files', 'files-ie');
        Config::set('queue.regions.au.files', 'files-au');

        $this->assertEquals('files-ie', region_queue_name('use-default'));
        $this->assertEquals('files-au', region_queue_name('au'));
    }

    public function testFilenameSanitisation(): void
    {
        $string = 'public/string';

        $this->assertSame('public-string', sanitise_filename($string));
    }

    public function testExplodeOptions(): void
    {
        $this->assertEquals([], explode_options(''));
        $this->assertEquals([], explode_options("\r\n"));
        $this->assertEquals(['option A', 'option B', 'option C'], explode_options("option A\r\noption B\r\noption C"));
        $this->assertEquals(['red', 'pink', 'green'], explode_options("red \r\n \r\n   \r\npink \r\ngreen"));
        $this->assertEquals(['0', '1', '2', '3'], explode_options("0\r\n1\r\n2\r\n3"));
    }

    public function testIsResizable(): void
    {
        $file = new File;

        $file->size = 15 * 1024 * 1024;
        $this->assertTrue(is_resizable($file));

        $file->size = 515 * 1024 * 1024;
        $this->assertFalse(is_resizable($file));
    }

    public function testExcelSheetTitle(): void
    {
        $title = '[Question] What is your **main** focus for today?';

        $this->assertEquals(excel_sheet_title($title), 'Question What is your main f...');
    }

    public function testMultibyteUppercaseFirst(): void
    {
        $this->assertEquals('Abc def', mb_ucfirst('abc def'));
        $this->assertEquals('Ósmę zĄb', mb_ucfirst('ósmę zĄb')); // Polish diacriticals
        $this->assertEquals('Ñiña', mb_ucfirst('ñiña')); // Spanish diacriticals
    }

    public function testHasLang(): void
    {
        $model = $this->mock(Field::class);
        $field = 'field';
        $model->shouldReceive('hasTranslation')->withArgs([$field])->once();
        has_lang($model, $field);
    }

    public function testSentenceCase(): void
    {
        $sentence = 'abd. deef. ggg hh. ii';
        $expected = 'Abd. Deef. Ggg hh. Ii';
        $this->assertEquals(sentence_case($sentence), $expected);
    }

    public function testMessage(): void
    {
        $message = message($txt = Str::random(), $title = Str::random(), $fwdTo = Str::random(), $btn = Str::random());
        $session = $message->response()->getSession();
        $this->assertEquals($session->get('message'), $txt);
        $this->assertEquals($session->get('title'), $title);
        $this->assertEquals($session->get('url'), $fwdTo);
        $this->assertEquals($session->get('buttonText'), $btn);
    }

    public function testFormFieldId(): void
    {
        $this->assertEquals(form_field_id('{slug}'), 'formField{slug}');
    }

    public function testLocaliseCollection(): void
    {
        $obj1 = new \stdClass();
        $obj1->field1 = 'ab';
        $obj1->field2 = 'cd';

        $obj2 = new \stdClass();
        $obj2->field1 = 'ef';
        $obj2->field2 = 'gh';

        $result = localise_collection(collect([$obj1, $obj2]), ['field1', 'field2']);
        $this->assertEquals($result, collect([0 => $obj1, 1 => $obj2]));
    }

    public function testFieldLabel(): void
    {
        Feature::shouldReceive('enabled')->with('ckeditor')->andReturnTrue();

        $obj = $this->mock(Entity::class);
        $obj->title = 'title';
        $obj->label = '';
        $this->assertEquals(field_label($obj), $obj->title);

        $obj->label = 'tests';
        $this->assertEquals(field_label($obj), "{$obj->label}");
    }

    public function testConsumer(): void
    {
        $this->assertEquals(consumer()->id(), $this->mockConsumer()->id());

        Consumer::shouldReceive('can')->with('update', 'resource')->andReturn(true);
        $this->assertTrue(consumer('update', 'resource'));

        Consumer::shouldReceive('can')->with('delete', 'resource')->andReturn(false);
        $this->assertFalse(consumer('delete', 'resource'));

        Consumer::shouldReceive('id')->andReturn(123);
        $this->assertEquals(consumer_id(), 123);
    }

    public function testConvertBoolToString(): void
    {
        $this->assertEquals(convert_bool_to_string(true), 'True');
        $this->assertEquals(convert_bool_to_string(false), 'False');
    }

    public function testLanguageCode(): void
    {
        $user = $this->mock(User::class);
        $membership = $this->mock(Membership::class);
        $user->shouldReceive('getAttribute')->once()->andReturn($membership);
        $membership->shouldReceive('getAttribute')->once()->andReturn('en_GB');

        language_code($user);
        $this->assertEquals(language_code(new \stdClass()), default_language_code());
    }

    public function testInvoiceNo(): void
    {
        $order = new Order();
        $order->id = 1234;
        $order->localId = 4321;
        $order->invoiceNumber = 3213;
        $this->assertEquals(invoice_no($order), 'INV-3213');
    }

    public function testPaymentCacheKey(): void
    {
        $this->assertEquals(payment_cache_key(123, 543), 'payment-123-543');
    }

    public function testDefaultAmount(): void
    {
        $price = $this->mock(Price::class);
        $price->shouldReceive('getTranslatableFields')->andReturn([]);
        $price->shouldReceive('getAttribute')->andReturn(['USD' => '120']);
        $amounts = collect([$price]);

        $this->assertEquals(default_amount($amounts, 'USD'), '$ 120.00');
    }

    public function testImgix(): void
    {
        CurrentAccount::shouldReceive('attribute')->andReturn('au');
        Feature::spy()->shouldReceive('enabled')->andReturnTrue()->byDefault();

        $imgix = imgix($file = 'folder/filename.jpg', 'original.jpg', ['h' => 900, 'w' => 100]);

        $this->assertStringContainsString($file, $imgix);
        $this->assertStringContainsString('h=900', $imgix);
        $this->assertStringContainsString('w=100', $imgix);
        $this->assertStringContainsString('auto=format', $imgix);

        $imgix = imgix('folder/filename.jpg', params: ['h' => 900, 'w' => 100, 'auto' => 'something']);
        $this->assertStringContainsString('auto=something', $imgix);

        $imgix = imgix('folder/filename.jpg', params: ['h' => 900, 'w' => 100, 'auto' => false]);
        $this->assertStringNotContainsString('auto=', $imgix);
    }

    public function testImgixFallbackToCloudAssetWhenImageOptimisationIsDisabled(): void
    {
        Feature::spy()->shouldReceive('enabled')->andReturnTrue()->byDefault()
            ->shouldReceive('enabled')->with('image_optimisation')->andReturnFalse();

        config(['filesystems.disks.s3-au.bucket_url' => 'test']);
        $resolved = imgix($file = 'folder/filename.jpg', params: ['h' => 900, 'w' => 100]);

        $this->assertEquals(cloud_asset_url($file), $resolved);
        $this->assertStringNotContainsString('w=100', $resolved);
    }

    public function testOnsiteGateway(): void
    {
        Setting::create(['key' => 'payment-gateway', 'value' => 'stripe']);
        Config::set('payment.onsite_gateways', ['stripe', 'sagepay']);
        $this->assertTrue(onsite_gateway());
    }

    public function testGatewayHas3ds2Challenge(): void
    {
        $settingRepository = $this->mock(SettingRepository::class);
        $settingRepository->shouldReceive('getValueByKey')->andReturn(null, 'securepay');
        $this->assertFalse(custom_checkout_form());

        $this->assertTrue(custom_checkout_form());
    }

    public function testOffsiteGateway(): void
    {
        $settingRepository = $this->mock(SettingRepository::class);
        $settingRepository->shouldReceive('getValueByKey')->andReturn('non-onsite-gateway');

        $this->assertFalse(onsite_gateway());
    }

    public function testOnsitePaymentMethod(): void
    {
        $this->assertTrue(onsite_payment_method('visa'));
    }

    public function testOffsitePaymentMethod(): void
    {
        $this->assertFalse(onsite_payment_method('invoice'));
    }

    public function testCardType(): void
    {
        Config::set('awardforce.extensions', ['videos' => ['mp4'], 'images' => ['jpg'], 'audio' => ['mp3']]);
        Feature::shouldReceive('enabled')->with('transcoding')->andReturn(true);

        $this->assertEquals('image', card_type(new File(['original' => 'file.jpg'])));
        $this->assertEquals('video', card_type(new File(['original' => 'file.mp4'])));
        $this->assertEquals('pdf', card_type(new File(['original' => 'file.pdf'])));
        $this->assertEquals('audio', card_type(new File(['original' => 'file.mp3'])));
        $this->assertEquals('generic', card_type(new File(['original' => 'file.doc'])));
    }

    public function testCardTypeTranscodingDisabled(): void
    {
        Feature::shouldReceive('enabled')->with('transcoding')->andReturn(false);
        $this->assertEquals('generic', card_type(new File(['original' => 'file.mp4'])));
    }

    public function testQrCode(): void
    {
        $text = 'testQR';
        $size = 50;
        $colour = ['r' => 12, 'g' => 13, 'b' => 25, 'a' => 0];
        $this->assertStringStartsWith('data:image/png;base64', qr_code($text, $size, $colour, $colour));
    }

    public function testFlattenKeyedArray(): void
    {
        $in = ['key' => ['val1', 'val2', 'val3']];
        $this->assertSame(flatten_keyed_array($in), ['key-val1', 'key-val2', 'key-val3']);
    }

    public function testSocialLoginUrl(): void
    {
        Config::shouldReceive('get')->with('app.auth-domain', null)->andReturn('testdomain');
        $account = new \stdClass();
        $account->globalId = Uuid::fromInteger(123);
        $account->slug = 'slug';
        $provider = 'prov';
        CurrentAccount::shouldReceive('get')->andReturn($account);

        $this->assertStringStartsWith("https://testdomain/social/authenticate/prov/{$account->globalId}", social_login_url($provider));
        $this->assertSame(social_login_url($provider, 'role'), "https://testdomain/social/authenticate/prov/{$account->globalId}?role=role");
    }

    public function testLocalisedNumberFormat(): void
    {
        $number = 12345.145;
        $languageCode = CurrentLocale::code();

        // TESTING INTEGERS
        $nf = new NumberFormatter($languageCode, NumberFormatter::DECIMAL);
        $nf->setAttribute(\NumberFormatter::FRACTION_DIGITS, 0);
        $this->assertTrue(localised_number_format($number, 0) === $nf->format($number));

        // TESTING DECIMALS
        $nf = new NumberFormatter($languageCode, NumberFormatter::DECIMAL);
        $nf->setAttribute(NumberFormatter::FRACTION_DIGITS, 1);
        $this->assertTrue(localised_number_format($number, 1) === $nf->format($number));

        // TESTING DECIMALS PRECISE
        $nf = new NumberFormatter($languageCode, NumberFormatter::DECIMAL);
        $nf->setAttribute(NumberFormatter::FRACTION_DIGITS, 2);
        $this->assertTrue(localised_number_format($number, 2) === $nf->format($number));

        // TESTING DECIMALS WITH DEFAULT DECIMAL PRECISION
        $nf = new NumberFormatter($languageCode, NumberFormatter::DECIMAL);
        $nf->setAttribute(\NumberFormatter::FRACTION_DIGITS, -1);
        $this->assertTrue(localised_number_format($number) === $nf->format($number));

        // TESTING INTEGERS WITH DEFAULT DECIMAL PRECISION
        $number = 12345;
        $nf = new NumberFormatter($languageCode, NumberFormatter::DECIMAL);
        $nf->setAttribute(\NumberFormatter::FRACTION_DIGITS, 0);
        $this->assertTrue(localised_number_format($number) === $nf->format($number));
    }

    public function testDisableButton(): void
    {
        $this->assertSame(disable_button(true), ' disabled="disabled"');
        $this->assertNull(disable_button(false));
    }

    public function testStateChangeColour(): void
    {
        $pos = rand(5, 90);
        $neg = $pos - 5;
        $this->assertSame(stat_change_colour(($pos - $neg), $pos, $neg), $pos);
        $this->assertSame(stat_change_colour(($neg - $pos), $pos, $neg), $neg);
    }

    public function testPublicAssetUrl(): void
    {
        CurrentAccount::shouldReceive('attribute')->with('region')->andReturn($region = Str::random(4));
        $key = Str::random(12);
        Config::shouldReceive('get')->with(
            'filesystems.disks.s3-'.$region.'.bucket_url',
            null
        )->andReturn($fs = Str::random(15).'.');
        $this->assertSame(public_asset_url($key), $fs.$key);
    }

    public function testCommentsToken(): void
    {
        $tag1 = new StringTag('tag1');
        $tag2 = new StringTag('tag2');
        $tag3 = new StringTag('tag3');
        $tags = [$tag1, $tag2, $tag3];
        $this->assertSame(comments_token($tags), md5(serialize($tags)));
    }

    public function testPreviewOrderComments(): void
    {
        $comments = m::mock(CommentCollection::class);
        $comments->shouldReceive('filter')->once()->andReturn($comments);
        $comments->shouldReceive('count')->once()->andReturn(2);
        $iterator = m::mock(\Iterator::class);
        $iterator->shouldReceive('rewind');
        $iterator->shouldReceive('valid');
        $comments->shouldReceive('getIterator')->andReturn($iterator);

        $comment = m::mock('comment');
        $comment->shouldReceive('hasTag')->andReturn(false);

        $order = m::mock(Order::class);
        $order->shouldReceive('getAttribute')->once();
        preview_order_comments($comments, $order);
    }

    public function testPreviewComments(): void
    {
        $comments = m::mock(CommentCollection::class);
        $comments->shouldReceive('count')->once()->andReturn(2);

        $iterator = m::mock(\Iterator::class);
        $iterator->shouldReceive('rewind');
        $iterator->shouldReceive('valid');
        $comments->shouldReceive('getIterator')->andReturn($iterator);

        preview_comments($comments);
    }

    public function testEncodeModalContent(): void
    {
        $view = m::spy(View::class);
        $view->shouldReceive('render')->andReturn('');
        \Illuminate\Support\Facades\View::shouldReceive('make')->once()->andReturn($view);
        encode_modal_content($view);
    }

    public function testFeatureDisabled(): void
    {
        Feature::shouldReceive('enabled')->once()->andReturn(false);
        $this->assertTrue(feature_disabled('test'));
    }

    public function testUrl2png(): void
    {
        $url = 'http://testurl.com';
        Config::shouldReceive('get')->once()->with('services.url2png.secret', null)->andReturn('secret');
        Config::shouldReceive('get')->once()->with('services.url2png.key', null)->andReturn('key');
        Config::shouldReceive('get')->once()->with('services.url2png.user_agent', null)->andReturn('bot');
        Config::shouldReceive('get')->once()->with('services.url2png.extra_options', null)->andReturn(['option' => 'value']);
        $this->assertStringStartsWith('https://api.url2png.com/v6/key/', url2png($url));
    }

    public function testUrl2pngOnlyRetunrOnlyValidURLs(): void
    {
        $validUrls = [
            'http://testurl.com',
            'http://testurl.com.au/test',
            'https://testurl.com/test',
            'https://testurl.com/test?test=1&test=2',
            'https://Сайтвпроцесірозробки.ки',
            'https://www.google.com/',
            'https://www.google.com.au/',
            'https://www.google.co.uk/',
            'https://t.me',
            'https://w3.org',
            'https://golde-nlion.com',
            'https://golde-nlion.co.uk',
            'https://www.goldenlion.com.ua',
            'https://www.golde-nlion.com',
            'https://www.golde-nlion.com.ua',
            'https://ssl-images-amazon.com/images/I/51Z%2B%2B7%2B%2BZ%2BL._AC_US218_.jpg',
            'https://www.tiktok.com/@sengay76/video/7121937657130732826?is_from_webapp=v1&item_id=7121937657130732826',
        ];

        $invalidUrls = [
            'https://google.comhttps://',
            'httpstesturl.com',
            'httpstesturl.com/test',
            'testurl.com/test?test=1',
            'https://testurl',
            'http://testurl',
            'ww.testurl.com',
            'www.testurl.com',
            'https://google.comhttps://',
            'https://+@forequality_mw',
            'https://https://www.tiktok.com/@nila20000?is_from_webapp=1&sender_device=pc',
            'https://.cookiescan.com',
        ];

        foreach ($validUrls as $url) {
            Config::shouldReceive('get')->once()->with('services.url2png.secret', null)->andReturn('secret');
            Config::shouldReceive('get')->once()->with('services.url2png.key', null)->andReturn('key');
            Config::shouldReceive('get')->once()->with('services.url2png.user_agent', null)->andReturn('bot');
            Config::shouldReceive('get')->once()->with('services.url2png.extra_options', null)->andReturn(['option' => 'value']);

            $this->assertTrue((bool) url2png($url));
        }

        foreach ($invalidUrls as $url) {
            $this->assertFalse((bool) url2png($url));
        }
    }

    public function testDebugTime(): void
    {
        VarDumper::setHandler(function () {
        });
        Log::shouldReceive('debug')->once();
        debug_time(Str::random(4));
    }

    public function testOverridableRedirect(): void
    {
        $this->assertStringContainsString('STRING', overridable_redirect('STRING'));
    }

    public function testSettingExplode(): void
    {
        Setting::create(['key' => 'testkey', 'value' => 'one,two,three']);
        $this->assertEquals(setting_explode('testkey'), [0 => 'one', 1 => 'two', 2 => 'three']);
    }

    public function testEntryCategoryShortcode(): void
    {
        $entry = new Entry;
        $entry->category()->associate($category = new Category);
        $category->addTranslation('en_GB', 'shortcode', 'test');

        $this->assertNotNull(lang($entry->category, 'shortcode'));
        $this->assertEquals(entry_category_shortcode($entry), lang($entry->category, 'shortcode'));
        $this->assertNull(entry_category_shortcode(new GrantReport));
    }

    public function testEntryCategoryShortCodeMissing(): void
    {
        $entry = new Entry;

        $entry->category()->associate($category = new Category);

        $this->assertEquals($entry->categoryId, $category->id);
        $this->assertNull(entry_category_shortcode($entry));
    }

    public function testForCategorySelect(): void
    {
        $category1 = new Category;
        $category1->id = 1;
        $category1->addTranslation('en_GB', 'name', 'Test category 1');

        $category2 = new Category;
        $category2->id = 2;
        $category2Name = 'Test category 2';
        $category2->addTranslation('en_GB', 'name', $category2Name);

        $cat = collect([$category1, $category2]);

        $service = new CategoryListGenerator(
            $categories = m::mock(CategoryRepository::class),
            $translator = m::mock(Engine::class)
        );

        $translator->shouldReceive('translate')->andReturn($cat);
        $categories->shouldReceive('getByIds')->once()->with([$category2->id])->andReturn($cat);
        app()->instance(CategoryListGenerator::class, $service);

        $result = for_category_select(collect([$category1, $category2]));

        $this->assertEquals(
            $result,
            [
                '' => '',
                $category1->id => $category1->name,
                $category2->id => $category2->name,
            ]
        );

        $category1->parentId = $category2->id;
        $this->assertEquals(get_parent_categories_as_a_string($category1), $category2->name.': ');
    }

    public function testTranslationsForVue(): void
    {
        $result = [
            'en_GB.miscellaneous' => [
                'optional' => trans('miscellaneous.optional'),
            ],
        ];

        $this->assertEquals($result, translations_for_vue('en_GB', ['miscellaneous.optional']));
    }

    public function testTranslationsForVueWithTerms(): void
    {
        $result = [
            'en_GB.miscellaneous' => [
                'optional' => trans('miscellaneous.optional'),
            ],
            'en_GB.terms-service' => [
                'reviewer' => [
                    'singular' => 'judge',
                    'plural' => 'judges',
                ],
            ],
        ];

        $this->assertEquals($result, translations_for_vue('en_GB', ['miscellaneous.optional'], ['reviewer']));
    }

    public function testTranslationsForVueWithReplacements(): void
    {
        $result = [
            'en_GB.miscellaneous' => [
                'optional' => trans('miscellaneous.optional'),
            ],
            'en_GB.billing' => [
                'messages' => [
                    'manual_invoicing' => trans('billing.messages.manual_invoicing', ['email' => '<EMAIL>']),
                ],
            ],
        ];

        $this->assertEquals($result, translations_for_vue('en_GB', ['miscellaneous.optional', 'billing.messages.manual_invoicing' => ['email' => '<EMAIL>']]));
    }

    public function testIdFromSlug(): void
    {
        $user = new User;
        $user->id = $id = User::max('id') + 1;
        $user->save();

        $this->assertSame($id, id_from_slug($slug = $user->slug, app(UserRepository::class)));
        $this->assertSame(null, id_from_slug(null, app(UserRepository::class)));
    }

    public function testSlugFromId(): void
    {
        $user = new User;
        $user->save();

        $this->assertSame((string) $user->slug, slug_from_id($user->id, app(UserRepository::class)));
        $this->assertNull(slug_from_id(123456, app(UserRepository::class)));
    }

    public function testIdsFromSlugs(): void
    {
        $user1 = new User;
        $user1->id = $id1 = User::max('id') + 1;
        $user1->save();
        $user2 = new User;
        $user2->id = $id2 = User::max('id') + 1;
        $user2->save();

        $slugs = ids_from_slugs([(string) $user1->slug, (string) $user2->slug], app(UserRepository::class));
        $this->assertTrue(in_array($id1, $slugs) && in_array($id2, $slugs));
        $this->assertSame([], ids_from_slugs(null, app(UserRepository::class)));
    }

    public function testCurrentAccountBrand(): void
    {
        $this->assertEquals(Account::BRAND_AWARDFORCE, current_account_brand());
        $this->assertNull(current_account_brand(false));
        $this->assertTrue(is_awardforce());
        $this->assertFalse(is_goodgrants());

        $this->account->brand = Account::BRAND_GOODGRANTS;

        $this->assertEquals(Account::BRAND_GOODGRANTS, current_account_brand());
        $this->assertEquals(Account::BRAND_GOODGRANTS, current_account_brand(false));
        $this->assertFalse(is_awardforce());
        $this->assertTrue(is_goodgrants());
    }

    public function testLangClass(): void
    {
        $times = 0;
        CurrentLocale::shouldReceive('code')->andReturnUsing(function () use (&$times) {
            return $times++ == 0 ? 'ar_AR' : 'en_GB';
        });

        $this->assertStringContainsString('rtl', lang_class());
        $this->assertStringNotContainsString('rtl', lang_class());
    }

    public function testPlansByBrand(): void
    {
        $plans = plans_by_brand('awardforce');
        $this->assertArrayHasKey('starter', $plans);
        $this->assertArrayNotHasKey('intro', $plans);

        $plans = plans_by_brand('goodgrants');
        $this->assertArrayHasKey('intro', $plans);
        $this->assertArrayNotHasKey('starter', $plans);
    }

    public function testProductsByBrand(): void
    {
        $products = products_by_brand('goodgrants');
        foreach ($products as $product) {
            $this->assertEquals('goodgrants', $product['brand']);
        }

        $products = products_by_brand('awardforce');
        foreach ($products as $product) {
            $this->assertEquals('awardforce', $product['brand']);
        }
    }

    public function testProductPrice(): void
    {
        $gateway = m::mock(PaymentSubscriptionGateway::class);
        $gateway->shouldReceive('getProductPrice')->with('usd-m-premium-2')->andReturn(123.12);
        app()->instance(PaymentSubscriptionGateway::class, $gateway);
        $this->assertEquals(123.12, product_price('usd-m-premium-2'));
    }

    public function testMakeProduct(): void
    {
        $product = make_product('eur', 'm', 'intro');
        $this->assertEquals('eur-m-intro-2', $product);
    }

    public function testTruncateUrl(): void
    {
        $url = 'https://af.test?token=oqunqcsxslawucjokopqwqqrfmjlkqsplekoevwdmwug';

        $this->assertSame(
            'https://af.test?token=oqunqcsxslawucjokopqwqqrfmjlkqsplekoev...',
            truncate_url($url)
        );

        $this->assertSame(
            '<a href="https://af.test?token=oqunqcsxslawucjokopqwqqrfmjlkqsplekoevwdmwug">https://af.test?token=oqunqcsxslawucjokopqwqqrfmjlkqsplekoev...</a>',
            truncate_url($url, true)
        );
    }

    public function testHubspotDeal(): void
    {
        $url = hubspot_url('deal', 1234);
        $this->assertStringContainsString('deal', $url);
        $this->assertStringContainsString(1234, $url);
        $this->assertStringNotContainsString('company', $url);

        $url = hubspot_url('company', 1234);
        $this->assertStringContainsString('company', $url);
        $this->assertStringContainsString(1234, $url);
        $this->assertStringNotContainsString('deal', $url);

        $this->expectException(\InvalidArgumentException::class);
        $url = hubspot_url('contract', 1234);
    }

    public function testTruncateFilename(): void
    {
        $normalFilename = 'this.is.a.normal.filename.pdf';
        $faker = Factory::create();
        $longFilename = $faker->lexify(str_repeat('?', 220)).'.pdf';

        $truncatedNormal = truncate_filename($normalFilename, 200);
        $truncatedLong = truncate_filename($longFilename, 200);

        $this->assertSame($normalFilename, $truncatedNormal);
        $this->assertEquals(200, strlen($truncatedLong));
        $this->assertStringEndsWith('.pdf', $longFilename);
    }

    public function testRemoveBreakingCharacters(): void
    {
        $text = 'This is a problematic text containing sucient characters';
        $this->assertEquals('This is a problematic text containing sucient characters', remove_breaking_characters($text));
    }

    public function testRemoveControlCharacters(): void
    {
        $controlCharacters = [
            "\x00",
            "\x01",
            "\x02",
            "\x03",
            "\x04",
            "\x05",
            "\x06",
            "\x07",
            "\x08",
            "\x09",
            "\x0A",
            "\x0B",
            "\x0C",
            "\x0D",
            "\x0E",
            "\x0F",
            "\x10",
            "\x11",
            "\x12",
            "\x13",
            "\x14",
            "\x15",
            "\x16",
            "\x17",
            "\x18",
            "\x19",
            "\x1A",
            "\x1B",
            "\x1C",
            "\x1D",
            "\x1E",
            "\x1F",
            "\x7F",
        ];

        foreach ($controlCharacters as $controlCharacter) {
            $badText = 'This is a problematic text containing control character '.$controlCharacter;
            $this->assertTrue(Str::contains($badText, $controlCharacter));
            $this->assertFalse(Str::contains(clean_export_string($badText), $controlCharacter));
        }
    }

    public function testCleansExportString(): void
    {
        $badText = "This is a problematic text containing control character \x01 \n";

        $this->assertTrue(Str::contains($badText, "\x01"));
        $this->assertTrue(Str::contains($badText, "\n"));

        $this->assertFalse(Str::contains(clean_export_string($badText), "\x01"));
        $this->assertFalse(Str::contains(clean_export_string($badText), "\n"));
    }

    public function testCleanExportStringPreservesUtf8(): void
    {
        $utf8String = 'This is a string with UTF-8 characters like Ελληνικά, Повторно or even 🦄';

        $this->assertTrue(Str::contains(clean_export_string($utf8String),
            [
                'Ελληνικά',
                'Повторно',
                '🦄',
            ]
        ));
    }

    public function testCleanExportStringDoesNotEncodeQuotes(): void
    {
        $string = 'This is a string with double "quotes" and single \'quotes\'';

        $this->assertEquals($string, clean_export_string($string));
    }

    public function testCleanExportStringDoesNotEncodeAmpersand()
    {
        $string = 'This is a string with an ampersand &';

        $this->assertEquals($string, clean_export_string($string));
    }

    public function testElevioHash(): void
    {
        $key = 998877;
        $email = '<EMAIL>';
        $mobile = '12345';
        $null = null;

        $this->assertSame(elevio_hash_generator($email, $key), hash_hmac('sha256', $email, $key));
        $this->assertSame(elevio_hash_generator($mobile, $key), hash_hmac('sha256', $mobile, $key));
        $this->assertSame(elevio_hash_generator((string) $null, $key), hash_hmac('sha256', (string) $null, $key));
    }

    public function testTransNoOverrides(): void
    {
        $key = 'review-flow.table.columns.entry_id';

        $trans = new Translation([
            'language' => trans()->getLocale(),
            'resource' => 'ui',
            'field' => $key,
            'value' => 'Overriding entry ID!',
        ]);
        $trans->accountId = current_account_id();
        $trans->save();

        $this->assertEquals('Overriding entry ID!', trans($key));
        $this->assertEquals('Entry ID', trans_no_overrides($key));
    }

    public function testSafeString(): void
    {
        $unsafeString = '<p>This string contains double quotes (&quot;), ampersand (&amp;) and xss in 2 versions: <script>alert("xss")</script> &amp; &lt;script&gt;alert(&quot;xss&quot;)&lt;/&gt;</p>';
        $this->assertEquals('This string contains double quotes ("), ampersand (&) and xss in 2 versions: alert("xss") & alert("xss")', safe_string($unsafeString));
    }

    public function testAvailableContentBlocks(): void
    {
        Feature::shouldReceive('enabled')->with('contracts')->once()->andReturn(true);
        Feature::shouldReceive('enabled')->with('eligibility')->twice()->andReturn(true);
        $availableContentBlocks = available_content_blocks();

        $this->assertTrue(in_array('contract', $availableContentBlocks));
        $this->assertTrue(in_array('entry-eligible', $availableContentBlocks));
        $this->assertTrue(in_array('entry-ineligible', $availableContentBlocks));

        Feature::shouldReceive('enabled')->with('contracts')->once()->andReturn(false);
        Feature::shouldReceive('enabled')->with('eligibility')->twice()->andReturn(false);
        $availableContentBlocks = available_content_blocks();

        $this->assertFalse(in_array('contract', $availableContentBlocks));
        $this->assertFalse(in_array('entry-eligible', $availableContentBlocks));
        $this->assertFalse(in_array('entry-ineligible', $availableContentBlocks));
    }

    public function testBlacklistUrl2Png(): void
    {
        $this->assertTrue(blacklisted_url2png('https://dropbox.com/a/file/path.extension'));

        $this->assertFalse(blacklisted_url2png('https://deopbox-not.com/a/file/path.extension'));

        $this->assertTrue(blacklisted_url2png('https://instagram.com/asdf'));
    }

    public function testAccountName(): void
    {
        $consumer = new MockConsumer;
        $consumer->language = new Language('es_LA');
        Consumer::set($consumer);

        $account = new Account;
        $account->save();
        $account->languages()->save(new SupportedLanguage(['code' => 'es_LA', 'default' => 1]));
        $account->saveTranslation('en_GB', 'name', 'name in English', $account->id);
        $account->saveTranslation('es_LA', 'name', $esName = 'nombre en español', $account->id);
        $account->save();
        CurrentAccount::swap(new CurrentAccountService($account));

        $this->assertSame($esName, account_name($account));
    }

    public function testIsEmail(): void
    {
        $this->assertTrue(is_email('<EMAIL>'));
        $this->assertFalse(is_email('not an email'));
    }

    public function testTranslatedProperty(): void
    {
        $languages = ['el_GR', 'en_GB'];
        $translations = [
            'en_GB' => ['name' => 'English name'],
            'el_GR' => ['name' => 'Greek name'],
        ];

        $this->assertEquals('Greek name', translated_property(
            $translations,
            'name',
            $languages
        ));

        // Primary language missing translations
        $languages = ['en_GB', 'el_GR'];
        $translations = [
            'en_GB' => [],
            'el_GR' => ['name' => 'Greek name'],
        ];

        $this->assertEquals('Greek name', translated_property(
            $translations,
            'name',
            $languages
        ));

        // $languages empty brings the first available translation
        $this->assertEquals('Greek name', translated_property(
            $translations,
            'name',
            []
        ));

        // Missing translation key
        $this->assertEquals(trans('miscellaneous.no_translation_available'), translated_property(
            $translations,
            'label',
            $languages
        ));

        // Primary language empty translations
        $translations = [
            'en_GB' => ['name' => ''],
            'el_GR' => ['name' => 'Greek name'],
        ];

        $this->assertEquals('Greek name', translated_property(
            $translations,
            'name',
            $languages
        ));
    }

    public function testGetFileName(): void
    {
        $files = [
            'somefile' => 'somefile.txt',
            'anotherfile' => 'anotherfile.pdf',
            'af4' => '/with/path/af4.png',
        ];
        foreach ($files as $expectedFilename => $file) {
            $this->assertEquals($expectedFilename, get_file_name($file));
        }
    }

    public function testTransElliptic(): void
    {
        $this->assertEquals(trans('any_translation').'...', trans_elliptic('any_translation'));
    }

    public function testShortenUrl(): void
    {
        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => $url = 'https://notapplicable.app/something', 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $this->assertEquals('https://cr4.ce/shortenedUrl', shorten_url($url));
    }

    public function testTransMergeSimple(): void
    {
        $translated = trans_merge('shared.manage', 'grants.titles.status');
        $grantStatus = trans('grants.titles.status');

        $this->assertStringContainsString(mb_ucfirst(trans('shared.manage')).' '.mb_strtolower($grantStatus), $translated);
    }

    public function testTransMergeWithReplacements(): void
    {
        $translated = trans_merge(['shared.table.empty', ['resource' => 'The REsOuRce']], 'shared.add');
        $expected = mb_ucfirst(mb_strtolower(trans('shared.table.empty', ['resource' => 'The REsOuRce'])).' '.mb_ucfirst(trans('shared.add')));

        $this->assertStringContainsString($expected, $translated);
    }

    public function testImplodeAndStripTags(): void
    {
        $array = ['Entry ID', 'Entry name', 'Comment'];
        $this->assertEquals(implode_and_strip_tags($array), 'Entry ID, Entry name, Comment');
    }

    public function testSafeDoubleQuotes(): void
    {
        $this->assertStringNotContainsString('"', safe_double_quotes('Replace "me.'));
        $this->assertStringContainsString('”', safe_double_quotes('Replace "me.'));
        $this->assertEquals(
            safe_double_quotes('Replace "me.'),
            'Replace ”me.'
        );
    }

    public function testAllowedFileExtension(): void
    {
        $this->assertTrue(allowed_file_extension('doc'));
        $this->assertFalse(allowed_file_extension('docasfjisadf'));
    }

    public function testItFetchesWebhookEventConfigProperly(): void
    {
        $this->assertEquals(1234, webhookEventConfig('test', 'qwe', 1234));

        $this->assertNull(webhookEventConfig('23453245', 'qwe'));
        $this->assertNull(webhookEventConfig('EntryWasUpdated', 'qwe'));

        $this->assertNotNull(webhookEventConfig('EntryWasUpdated', 'delay'));
        $this->assertNotNull(webhookEventConfig('EntryWasUpdated', 'debounce_key'));

        $this->assertEquals(300, webhookEventConfig('EntryWasUpdated', 'delay'));
        $this->assertEquals('slug', webhookEventConfig('EntryWasUpdated', 'debounce_key'));
    }

    public function testWebhookPayloadHasDebounce(): void
    {
        $this->assertNull(webhookEventConfig(null, 'asdfasdf', null));
        $this->assertNull(webhookEventConfig('asdfasdfsadf', 'sdafsdfgdfg', null));
        $this->assertNull(webhookEventConfig('dfsdfgsdfgsdfg', 'debounce_key', null));
        $this->assertNull(webhookEventConfig('EntryWasUpdated', 'sdfhdfgjhfgkj', null));
        $this->assertEquals('slug', webhookEventConfig('EntryWasUpdated', 'debounce_key', null));
    }

    public function testWebhookPayloadHasDelay(): void
    {
        $this->assertEquals(0, webhookEventConfig(null, 'asdfasdf', 0));
        $this->assertEquals(0, webhookEventConfig('asdfasdfsadf', 'sdafsdfgdfg', 0));
        $this->assertEquals(0, webhookEventConfig('dfsdfgsdfgsdfg', 'delay', 0));
        $this->assertEquals(0, webhookEventConfig('EntryWasUpdated', 'sdfhdfgjhfgkj', 0));
        $this->assertEquals(300, webhookEventConfig('EntryWasUpdated', 'delay', 0));
    }

    public function testHasMultipleChapters(): void
    {
        CurrentAccount::shouldReceive('get')->andReturn($this->account);
        Feature::shouldReceive('enabled')->with('multi_chapter')->andReturn(true);

        $this->account->chapterQuantityLimit = 1;
        $this->assertFalse(has_multiple_chapters());

        $this->account->chapterQuantityLimit = 2;
        $this->assertTrue(has_multiple_chapters());
    }

    public function testItReturnsProperlyIfGivenDatesAreInRange(): void
    {
        $this->assertTrue(dateIsInRange());

        $this->assertFalse(dateIsInRange(
            to: now()->format('Y-m-d H:i:s'),
            toTimezone: 'Europe/Athens',
            date: now()->addDay()->setTimezone('Europe/Athens')
        ));
        $this->assertTrue(dateIsInRange(
            to: now()->addDay()->format('Y-m-d H:i:s'),
            toTimezone: 'Europe/Athens',
            date: now()->setTimezone('Europe/Athens')
        ));

        $this->assertFalse(dateIsInRange(
            to: now()->subDay()->format('Y-m-d H:i:s'),
            toTimezone: 'Europe/Athens'
        ));
        $this->assertTrue(dateIsInRange(
            to: now()->addDay()->format('Y-m-d H:i:s'),
            toTimezone: 'Europe/Athens'
        ));
        $this->assertFalse(dateIsInRange(
            from: now()->addDay()->format('Y-m-d H:i:s'),
            fromTimezone: 'Europe/Athens'
        ));
        $this->assertTrue(dateIsInRange(
            from: now()->subDay()->format('Y-m-d H:i:s'),
            fromTimezone: 'Europe/Athens'
        ));
        $this->assertFalse(dateIsInRange(
            from: now()->subDays(10)->format('Y-m-d H:i:s'),
            fromTimezone: 'Europe/Athens',
            to: now()->subDays(5)->format('Y-m-d H:i:s'),
            toTimezone: 'Europe/Athens'
        ));
        $this->assertTrue(dateIsInRange(
            from: now()->subDays(10)->format('Y-m-d H:i:s'),
            fromTimezone: 'Europe/Athens',
            to: now()->addDays(10)->format('Y-m-d H:i:s'),
            toTimezone: 'Europe/Athens'
        ));
        $this->assertFalse(dateIsInRange(
            from: now()->addDays(5)->format('Y-m-d H:i:s'),
            fromTimezone: 'Europe/Athens',
            to: now()->addDays(10)->format('Y-m-d H:i:s'),
            toTimezone: 'Europe/Athens'
        ));
    }

    public function testIsCurrentConsumer(): void
    {
        Consumer::shouldReceive('is')->with(null)->andReturn(false);

        $this->assertFalse(is_current_consumer(null));

        Consumer::shouldReceive('is')->with(123)->andReturn(true);

        $this->assertTrue(is_current_consumer(123));
    }

    public function testSlugs(): void
    {
        $obj1 = new \stdClass();
        $obj1->slug = 'slug1';

        $obj2 = new \stdClass;
        $obj2->slug = 'slug2';

        $obj3 = new \stdClass;
        $obj3->slug = 'slug3';

        $collection = new Collection([$obj1, $obj2, $obj3]);
        $slugs = slugs($collection);

        $this->assertCount(3, $slugs);
        $this->assertArrayNotHasKey('slug', $slugs);
        $this->assertEquals($obj1->slug, $slugs[0]);
        $this->assertEquals($obj3->slug, $slugs[2]);
    }

    public function testItReturnsAnArrayWithoutAValue(): void
    {
        $modes = ['asd', 'qwe', 'rty'];
        $this->assertEquals(['asd', 'rty'], array_without_value($modes, 'qwe'));
        $this->assertEquals(['asd', 'qwe', 'rty'], array_without_value($modes, '123456'));
    }

    public function testItChecksIfIsGuestContext(): void
    {
        $this->assertFalse(isGuestContext());
        $this->session([ContextService::SELECTED_CONTEXT_KEY => Guest::name()]);
        $this->assertTrue(isGuestContext());
    }

    public function testItFetchesCurrentAccountSubscriptionCustomerId(): void
    {
        CurrentAccount::shouldReceive('get')->andReturn($this->account);
        $this->assertNull(current_account_subscription_customer_id());

        $this->account->subscription_customer_id = 123456;
        CurrentAccount::shouldReceive('get')->andReturn($this->account);
        $this->assertEquals(123456, current_account_subscription_customer_id());
    }

    public function testItFetchesCurrentAccountSubscriptionId(): void
    {
        CurrentAccount::shouldReceive('get')->andReturn($this->account);
        $this->assertNull(current_account_subscription_id());

        $this->account->subscription_id = 123456;
        CurrentAccount::shouldReceive('get')->andReturn($this->account);
        $this->assertEquals(123456, current_account_subscription_id());
    }

    public function testItFetchesConsumerLocaleForChargebee(): void
    {
        CurrentAccount::shouldReceive('get')->andReturn($this->account);
        $this->assertEquals('en-GB', get_consumer_locale_for_chargebee());
    }

    public function testExcelEscape(): void
    {
        $text1 = 'test';
        $text2 = '=test';
        $text3 = '+test';
        $text4 = '-test';
        $text5 = 'test-2';
        $text6 = '🚧test';
        $text7 = '=🚧test';

        $this->assertEquals($text1, excel_escape($text1));
        $this->assertEquals("'".$text2, excel_escape($text2));
        $this->assertEquals("'".$text3, excel_escape($text3));
        $this->assertEquals("'".$text4, excel_escape($text4));
        $this->assertEquals($text5, excel_escape($text5));
        $this->assertEquals($text6, excel_escape($text6));
        $this->assertEquals("'".$text7, excel_escape($text7));
    }

    public function testEmptyToNull(): void
    {
        $this->assertNull(emptyToNull(''));
        $this->assertNull(emptyToNull(' '));
        $this->assertNotNull(emptyToNull('abc'));
        $this->assertNotNull(emptyToNull(123));
    }

    public function testHasActiveGrantFeatures(): void
    {
        Feature::shouldReceive('enabled')->with('contracts')->andReturn(false);
        Feature::shouldReceive('enabled')->with('fund_management')->andReturn(true);
        Feature::shouldReceive('enabled')->with('grants')->andReturn(true);
        Feature::shouldReceive('enabled')->with('grant_reports')->andReturn(true);

        $this->assertTrue(has_active_grant_features());
    }

    public function testRemoveQueryParams(): void
    {
        $url = 'https://example.com/page?param1=value1&param2=value2';
        $this->assertEquals('https://example.com/page', remove_query_params($url));
    }

    public function testHasActiveGrantFeaturesFalse(): void
    {
        Feature::shouldReceive('enabled')->with('contracts')->andReturn(false);
        Feature::shouldReceive('enabled')->with('fund_management')->andReturn(false);
        Feature::shouldReceive('enabled')->with('grants')->andReturn(false);
        Feature::shouldReceive('enabled')->with('grant_reports')->andReturn(false);

        $this->assertFalse(has_active_grant_features());
    }

    public function testCollectionMapper(): void
    {
        $obj1 = new \stdClass();
        $obj1->a = '11a';
        $obj1->b = '11b';
        $obj1->c = '11c';

        $obj2 = new \stdClass;
        $obj2->a = '22a';
        $obj2->b = '22b';
        $obj2->c = '22c';

        $obj3 = new \stdClass;
        $obj3->a = '33a';
        $obj3->b = '33b';
        $obj3->c = '33c';

        $collection = collect([$obj1, $obj2, $obj3]);
        $result1 = collection_mapper($collection, ['a', 'b'])->toArray();

        $this->assertEquals($obj1->a, $result1[0]['a']);
        $this->assertEquals($obj1->b, $result1[0]['b']);
        $this->assertEquals($obj2->a, $result1[1]['a']);
        $this->assertEquals($obj2->b, $result1[1]['b']);

        $this->assertArrayNotHasKey('c', $result1[0]);
        $this->assertArrayNotHasKey('c', $result1[1]);

        $result2 = collection_mapper($collection, ['a' => 'a', 'b', 'c' => 'a'])->toArray();

        $this->assertEquals($obj1->a, $result2[0]['a']);
        $this->assertEquals($obj1->b, $result2[0]['b']);
        $this->assertEquals($obj1->a, $result2[0]['c']);

        $this->assertEquals($obj2->a, $result2[1]['a']);
        $this->assertEquals($obj2->b, $result2[1]['b']);
        $this->assertEquals($obj2->a, $result2[1]['c']);
    }

    public function testReplaceDeep()
    {
        $input = [
            'ar_AR' => [
                'title' => 'A caat in a house',
                'content' => 'Lorem ipsum dolor sit amet',
            ],
        ];

        $arrayableInput = new class implements Arrayable
        {
            public function toArray()
            {
                return [
                    'ar_AR' => [
                        'title' => 'A caat in a house',
                        'content' => 'Lorem ipsum dolor sit amet',
                    ],
                ];
            }
        };

        $expected = [
            'ar_AR' => [
                'title' => 'A c!@!@t in !@ house',
                'content' => 'Lorem ipsum dolor sit !@met',
            ],
        ];

        $this->assertEquals($expected, replace_deep('a', '!@', $input));
        $this->assertEquals($expected, replace_deep('a', '!@', $arrayableInput));
        $this->assertEquals([], replace_deep('a', '!@', null));
    }

    #[TestWith(['https://example.com'])]
    #[TestWith(['http://example.com'])]
    #[TestWith(['example.com'])]
    public function testHttpsToUrl(string $baseUrl)
    {
        $this->assertEquals('https://example.com', enforce_url_https($baseUrl));
    }

    public function testDecodeSpecialChars()
    {
        $string1 = 'text with < > and &';
        $string2 = 'text with ampersand &amp;';
        $string3 = 'text with greater than &gt;';
        $string4 = 'text with less than &lt;';
        $string5 = 'all &amp; &lt; &gt; in the same string';

        $this->assertSame($string1, decode_special_chars($string1));
        $this->assertSame('text with ampersand &', decode_special_chars($string2));
        $this->assertSame('text with greater than >', decode_special_chars($string3));
        $this->assertSame('text with less than <', decode_special_chars($string4));
        $this->assertSame('all & < > in the same string', decode_special_chars($string5));
    }

    public function testThrottlingCanNotBeDisabledInProductionEnvironment(): void
    {
        app()->detectEnvironment(fn() => 'production');

        config(['awardforce.throttle_requests' => true]);
        $this->assertFalse(throttling_disabled());

        config(['awardforce.throttle_requests' => false]);
        $this->assertFalse(throttling_disabled());
    }

    public function testThrottlingCanBeDisabledInNonProductionEnvironment(): void
    {
        app()->detectEnvironment(fn() => 'local');

        config(['awardforce.throttle_requests' => true]);
        $this->assertFalse(throttling_disabled());

        config(['awardforce.throttle_requests' => false]);
        $this->assertTrue(throttling_disabled());
    }

    public function testCleanExportReplaceNewLineWithSpaces()
    {
        $text = "This text contains a new line\r\nhere";
        $this->assertEquals('This text contains a new line here', clean_export_string($text));
    }

    #[TestWith([10, 5, 0.0001, true])]
    #[TestWith([10, 0.5, 0.0001, true])]
    #[TestWith([0.8, 0.2, 0.0001, true])]
    #[TestWith([0.8, 0.3, 0.0001, false])]
    #[TestWith([10, 0.25, 0.1, true])]
    #[TestWith([1.2, 0.3, 0.01, true])]
    #[TestWith([1.2, 0.4, 0.01, true])]
    #[TestWith([1.2, 0.5, 0.01, false])]
    public function testIsMultiple(float $number, float $multiple, float $epsilon, bool $expected): void
    {
        $this->assertEquals($expected, is_multiple($number, $multiple, $epsilon));
    }

    #[TestWith([['subject' => ['en_gb' => 'Subject', 'fr_fr' => 'Sujet']], ['en_gb' => ['subject' => 'Subject'], 'fr_fr' => ['subject' => 'Sujet']]])]
    #[TestWith([null, []])]
    public function testTranslationsFromRequest($translations, $expected)
    {
        $this->assertEquals($expected, translations_from_request($translations));
    }

    #[TestWith([null, '7564-cat123'])]
    #[TestWith([LocalIdShortcodeFormat::After, '7564-cat123'])]
    #[TestWith([LocalIdShortcodeFormat::Before, 'cat123-7564'])]
    public function testLocalIdUsesCorrectOrderingBasedOnShortcodeFormatSetting(?LocalIdShortcodeFormat $settingValue, string $expected): void
    {
        $entry = m::mock(Entry::class)->makePartial();
        $entry->localId = 7564;
        $shortcode = 'cat123';
        $entry->shouldReceive('categoryShortcode')->andReturn($shortcode);
        $this->instance(SettingRepository::class, $settings = m::mock(SettingRepository::class));

        $settings->shouldReceive('getValueByKey')->with('local-id-shortcode-format', null)->andReturn($settingValue?->value);

        $this->assertEquals($expected, local_id($entry));
    }
}

class TestCloudFrontExpires
{
    public function getSignedUrl(array $options): int
    {
        return (int) $options['expires'];
    }
}
