<?php

namespace AwardForce\Library\AIAgents\Enums;

use Illuminate\Support\Collection;
use Prism\Prism\Enums\Provider as Vendor;
use RuntimeException;

enum Model: string
{
    case ClaudeSonnet4 = 'claude_sonnet_4';
    case DeepSeekR1 = 'deepseek_r1';
    case GptOss120b = 'gpt_oss_120b';
    case MistralLarge = 'mistral_large';

    public function provider(): Provider
    {
        return Provider::Bedrock;
    }

    public function modelId(string $region): string
    {
        if (! in_array($region, array_get($this->metadata(), 'supportedRegions'), true)) {
            throw new RuntimeException("Model {$this->name} is not available in {$region} region");
        }

        $modelId = array_get($this->metadata(), 'modelId');

        if ($this->hasCrossRegionSupport($region)) {
            $modelId = array_get($this->regionMapping(), $region, $region).".$modelId";
        }

        return $modelId;
    }

    /**
     * @return array{
     *     name: string,
     *     vendor: Vendor,
     *     modelId: string,
     *     supportedRegions: string[],
     *     crossRegions: string[],
     * }
     */
    public function metadata(): array
    {
        return match ($this) {
            self::ClaudeSonnet4 => [
                'name' => 'Claude Sonnet 4',
                'vendor' => Vendor::Anthropic,
                'modelId' => 'anthropic.claude-sonnet-4-20250514-v1:0',
                'supportedRegions' => ['eu', 'au', 'us', 'ca', 'hk'],
                'crossRegions' => ['eu', 'au', 'us', 'ca', 'hk'],
            ],
            self::DeepSeekR1 => [
                'name' => 'DeepSeek R1',
                'vendor' => Vendor::DeepSeek,
                'modelId' => 'deepseek.r1-v1:0',
                'supportedRegions' => ['us'],
                'crossRegions' => ['us'],
            ],
            self::GptOss120b => [
                'name' => 'GPT OSS 120B',
                'vendor' => Vendor::OpenAI,
                'modelId' => 'openai.gpt-oss-120b-1:0',
                'supportedRegions' => ['us'],
                'crossRegions' => [],
            ],
            self::MistralLarge => [
                'name' => 'Mistral Large',
                'vendor' => Vendor::Mistral,
                'modelId' => 'mistral.mistral-large-2402-v1:0',
                'supportedRegions' => ['eu', 'au', 'us', 'ca'],
                'crossRegions' => [],
            ],
        };
    }

    /**
     * Some regions have a different naming convention from our own, or even differ from AWS' own region codes. In
     * these cases, we need to store this map here, so that we can easily lookup the AWS region for the app's
     * region.
     */
    private function regionMapping(): array
    {
        return [
            'au' => 'apac',
            'hk' => 'apac',
            'ca' => 'us',
        ];
    }

    /**
     * Returns true if the region has cross-region support. When this is true, we want to defer to cross-region use
     * of the models, rather than use them directly. This is meant to result in better and faster experience with
     * the Bedrock models.
     */
    private function hasCrossRegionSupport(string $region): bool
    {
        return in_array($region, array_get($this->metadata(), 'crossRegions'), true);
    }

    /**
     * @return Model[]
     */
    public static function casesFor(string $region): array
    {
        return collect(self::cases())
            ->filter(static fn(Model $model) => $model->availableIn($region))
            ->values()
            ->all();
    }

    public function availableIn(string $region): bool
    {
        return in_array($region, array_get($this->metadata(), 'supportedRegions'), true);
    }

    public function name(string $region): string
    {
        $modelName = array_get($this->metadata(), 'name');

        return $this->hasCrossRegionSupport($region) && $this->internationalDataTransferRequired($region)
            ? trans('ai-agents.model.name', ['model' => $modelName])
            : $modelName;
    }

    /**
     * Get one model for each vendor in the given region.
     *
     * @return Model[]
     */
    public static function latestFor(string $region): array
    {
        return collect(self::casesFor($region))
            ->groupBy(fn(Model $model) => array_get($model->metadata(), 'vendor'))
            ->map(fn(Collection $models) => $models->first())
            ->values()
            ->all();
    }

    private function internationalDataTransferRequired(string $region): bool
    {
        return array_key_exists($region, $this->regionMapping());
    }
}
