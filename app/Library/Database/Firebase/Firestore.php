<?php

namespace AwardForce\Library\Database\Firebase;

use DateTime;
use Eloquence\Behaviours\Slug;
use Google\Cloud\Core\Timestamp;
use Illuminate\Support\Arr;
use InvalidArgumentException;
use Kreait\Firebase\Contract\Firestore as FirestoreContract;
use Kreait\Firebase\Exception\AuthException;
use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Laravel\Firebase\Facades\Firebase;

readonly class Firestore implements Database
{
    public function __construct(public FirestoreContract $firestore)
    {
    }

    public function get(Path $path): mixed
    {
        try {
            return $this->firestore->database()
                ->collection($path->collection)
                ->document($path->documentId)
                ->snapshot()
                ->get($path->fieldPath);
        } catch (InvalidArgumentException) {
            return null;
        }
    }

    public function set(Path $path, mixed $value): void
    {
        $this->firestore->database()
            ->collection($path->collection)
            ->document($path->documentId)
            ->set($this->prepareValues($path->fieldPath, $value), ['merge' => true]);
    }

    /**
     * @throws AuthException
     * @throws FirebaseException
     */
    public function generateAuthToken(Slug $userSlug, Claim $claim): string
    {
        return Firebase::auth()->createCustomToken(current_account_slug().$userSlug, $claim->get())->toString();
    }

    public function expireAt(): Timestamp
    {
        return new Timestamp(new DateTime(now()->addSeconds(config('firebase.ttl'))->toIsoString()));
    }

    private function prepareValues(string $path, mixed $value): array
    {
        return Arr::undot(array_merge([$path => $value], ['expireAt' => $this->expireAt()]));
    }
}
