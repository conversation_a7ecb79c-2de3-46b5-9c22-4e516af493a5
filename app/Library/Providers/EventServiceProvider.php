<?php

namespace AwardForce\Library\Providers;

use AwardForce\Modules\Accounts\Events\AccountWasCreated;
use AwardForce\Modules\AllocationPayments\Events\AllocationPaymentStatusWasChanged;
use AwardForce\Modules\AllocationPayments\Events\AllocationPaymentWasCreated;
use AwardForce\Modules\AllocationPayments\Events\AllocationPaymentWasDeleted;
use AwardForce\Modules\AllocationPayments\Events\AllocationPaymentWasUpdated;
use AwardForce\Modules\AllocationPayments\Listeners\AllocationPaymentsDueListener;
use AwardForce\Modules\AllocationPayments\Listeners\AllocationPaymentsStatusListener;
use AwardForce\Modules\Assignments\Events\AssignmentWasCompletedByJudge;
use AwardForce\Modules\Assignments\Events\AssignmentWasCreated;
use AwardForce\Modules\Assignments\Events\CategoryChapterProcessor;
use AwardForce\Modules\Assignments\Events\RoleGrantedProcessor;
use AwardForce\Modules\Assignments\Events\TagsProcessor;
use AwardForce\Modules\Awards\Events\SeasonCopierListener as AwardsSeasonCopierListener;
use AwardForce\Modules\Broadcasts\Models\RecipientsPreparer;
use AwardForce\Modules\Categories\Events\CategoryDivisionsWereChanged;
use AwardForce\Modules\Categories\Events\CopierListener as CategoriesCopierListener;
use AwardForce\Modules\Chapters\Listeners\SeasonCopierListener as ChaptersSeasonCopierListener;
use AwardForce\Modules\Documents\Events\DocumentWasCreated;
use AwardForce\Modules\Ecommerce\Cart\Events\CartWasUpdated;
use AwardForce\Modules\Ecommerce\Cart\Events\ItemWasAddedToCart;
use AwardForce\Modules\Ecommerce\Cart\Listeners\CartListener;
use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasDeleted;
use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasPaid;
use AwardForce\Modules\Entries\Events\CategoryWasChanged;
use AwardForce\Modules\Entries\Events\EntryHealthWasCleared;
use AwardForce\Modules\Entries\Events\EntryHealthWasFlagged;
use AwardForce\Modules\Entries\Events\EntryResubmissionWasRequired;
use AwardForce\Modules\Entries\Events\EntryWasEligible;
use AwardForce\Modules\Entries\Events\EntryWasIneligible;
use AwardForce\Modules\Entries\Events\EntryWasInvited;
use AwardForce\Modules\Entries\Events\EntryWasModerated;
use AwardForce\Modules\Entries\Events\EntryWasResubmitted;
use AwardForce\Modules\Entries\Events\EntryWasSubmitted;
use AwardForce\Modules\Entries\Events\EntryWasUpdated;
use AwardForce\Modules\Entries\Listeners\EntryHealthListener;
use AwardForce\Modules\Entries\Listeners\ReassignEntryDivisionsListener;
use AwardForce\Modules\Entries\Listeners\UpdateEntryDivisionListener;
use AwardForce\Modules\Files\Services\Thumbnails\ThumbnailsListener;
use AwardForce\Modules\Forms\Collaboration\Events\CollaboratorWasInvited;
use AwardForce\Modules\Forms\Fields\Events\FieldWasCopied;
use AwardForce\Modules\Forms\Forms\Events\CopierWasStarted as FormCopierWasStarted;
use AwardForce\Modules\Forms\Forms\Events\Listeners\SeedDefault;
use AwardForce\Modules\Forms\Tabs\Services\Eligibility\EligibleTabsEventListener;
use AwardForce\Modules\GrantReports\Events\GrantReportDueDateWasUpdated;
use AwardForce\Modules\GrantReports\Events\GrantReportWasCreated;
use AwardForce\Modules\GrantReports\Events\GrantReportWasSubmitted;
use AwardForce\Modules\GrantReports\Listeners\ActiveFiltersListener;
use AwardForce\Modules\GrantReports\Listeners\GrantReportsListener;
use AwardForce\Modules\Grants\Events\GrantStatusWasChanged;
use AwardForce\Modules\Identity\Users\Events\GlobalCommunicationChannelWasAdded;
use AwardForce\Modules\Identity\Users\Events\RoleWasGranted;
use AwardForce\Modules\Identity\Users\Events\UserHasRegistered;
use AwardForce\Modules\Identity\Users\Events\UserWasAdded;
use AwardForce\Modules\Identity\Users\Events\UserWasInvited;
use AwardForce\Modules\Integrations\Listeners\SeasonCopier as IntegrationsCopierListener;
use AwardForce\Modules\Notifications\Events\SystemEventListener;
use AwardForce\Modules\Notifications\Listeners\SeasonCopier as NotificationsCopierListener;
use AwardForce\Modules\ReviewFlow\Events\ReviewStageWasDeleted;
use AwardForce\Modules\ReviewFlow\Events\ReviewStageWasRestored;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskProcessor;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasCreated;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasProceeded;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasReassigned;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasStopped;
use AwardForce\Modules\ReviewFlow\Events\SeasonCopierListener as ReviewFlowCopierListener;
use AwardForce\Modules\ReviewFlow\Events\SendReviewTaskNotificationWasRequested;
use AwardForce\Modules\ReviewFlow\Listeners\ForgetReviewTaskCountCache;
use AwardForce\Modules\ReviewFlow\Listeners\ReviewFlowListener;
use AwardForce\Modules\Rounds\Listeners\CopierListener as RoundsCopierListener;
use AwardForce\Modules\ScoreSets\Events\CopierListener as ScoreSetsCopierListener;
use AwardForce\Modules\ScoreSets\Events\ScoreSetConfigurationWasUpdated;
use AwardForce\Modules\ScoreSets\Events\ScoreSetRecalculationListener;
use AwardForce\Modules\ScoringCriteria\Events\CopierListener as ScoringCriteriaCopierListener;
use AwardForce\Modules\ScoringCriteria\Events\ScoringCriterionWasCopied;
use AwardForce\Modules\Seasons\Events\CopierWasStarted as SeasonCopierWasStarted;
use AwardForce\Modules\Seasons\Events\EmptySeasonWasCreated;
use AwardForce\Modules\Tags\Events\TagWasAdded;
use AwardForce\Modules\Tags\Events\TagWasAddedAutomatically;
use Platform\Events\Dispatcher;
use Platform\Events\ModelWasCopied;
use Platform\Localisation\Listeners\CopyTranslationsListener;

class EventServiceProvider extends \Illuminate\Foundation\Support\Providers\EventServiceProvider
{
    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        \AwardForce\Modules\Accounts\Listeners\KesselListener::class,
        \AwardForce\Modules\Accounts\Listeners\DestroyerListener::class,
        \AwardForce\Modules\Assignments\Events\ScoreSetSyncListener::class,
        \AwardForce\Modules\Assignments\Events\RoleGrantedProcessor::class,
        \AwardForce\Modules\Assignments\Events\TagsProcessor::class,
        \AwardForce\Modules\Assignments\Events\CategoryChapterProcessor::class,
        \AwardForce\Modules\Audit\Events\Auditor::class,
        \AwardForce\Modules\Awards\Events\AwardSubscriber::class,
        \AwardForce\Modules\Files\Listeners\RefreshUsage::class,
        \AwardForce\Modules\Judging\Events\CacheFlusher::class,
        \AwardForce\Modules\Tags\Services\Tagger::class,
        \AwardForce\Modules\ReviewFlow\Events\ReviewTaskProcessor::class,
    ];

    protected $listen = [
        // String key events
        'active.filters' => [
            \AwardForce\Modules\Assignments\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Audit\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\AllocationPayments\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Categories\Events\CategoryActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Categories\Events\CategoryStatusActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Categories\Events\ParentActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Chapters\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Chapters\Events\ChapterStatusActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Content\Blocks\Listeners\ActiveFilters::class.'@handle',
            \AwardForce\Modules\Ecommerce\Cart\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Ecommerce\Orders\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Entries\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Documents\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Forms\Tabs\Search\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Forms\Fields\Search\Listeners\SearchableFieldsActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Identity\Roles\Listeners\ActiveFilters::class.'@handle',
            \AwardForce\Modules\Identity\Users\Listeners\ActiveFilters::class.'@handle',
            \AwardForce\Modules\Judging\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Judging\Listeners\Progress\ActiveFilters::class.'@handle',
            \AwardForce\Modules\Notifications\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Payments\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\ReviewFlow\Events\ActiveFiltersLabel::class.'@handle',
            \AwardForce\Modules\ScoreSets\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\ScoringCriteria\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Search\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Tags\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Webhooks\Events\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Contract\Listeners\ContractActiveFilterListener::class.'@handle',
            \AwardForce\Modules\Grants\Listeners\ActiveFiltersListener::class.'@handle',
            \AwardForce\Modules\Awards\Listeners\ActiveFiltersListener::class.'@handle',
            ActiveFiltersListener::class.'@handle',
        ],
        \Illuminate\Auth\Events\Login::class => [
            'AwardForce\Modules\Authentication\Events\AuthLogin@whenUserHasLoggedIn',
            'AwardForce\Modules\Agreements\Listeners\StoreCookieConsent@whenUserHasLoggedIn',
            'AwardForce\Modules\Rounds\Listeners\RoundStatus@whenUserHasLoggedIn',
        ],

        // Fired by Laravel for App::setLocale()
        \Illuminate\Foundation\Events\LocaleUpdated::class => [
            \Platform\Localisation\Listeners\UpdateCarbonLocale::class,
        ],

        // Queued job failed
        \Illuminate\Queue\Events\JobFailed::class => [
            \AwardForce\Library\Bus\LogFailedJobs::class,
        ],

        RecipientsPreparer::class => [
            'AwardForce\Modules\Assignments\Listeners\AssignmentRecipients@handle',
            'AwardForce\Modules\Entries\Listeners\EntryRecipients@handle',
            'AwardForce\Modules\Panels\Listeners\PanelRecipients@handle',
            'AwardForce\Modules\Identity\Users\Listeners\UserRecipients@handle',
            'AwardForce\Modules\ReviewFlow\Listeners\ReviewTaskRecipients@handle',
            'AwardForce\Modules\Funding\Listeners\AllocationRecipients@handle',
            'AwardForce\Modules\AllocationPayments\Listeners\AllocationPaymentRecipients@handle',

            'AwardForce\Modules\Judging\Listeners\Leaderboard\QualifyingRecipients@handle',
            'AwardForce\Modules\Judging\Listeners\Leaderboard\VotingRecipients@handle',
            'AwardForce\Modules\Judging\Listeners\Leaderboard\TopPickRecipients@handle',
            'AwardForce\Modules\Judging\Listeners\Leaderboard\VipJudgingRecipients@handle',

            'AwardForce\Modules\Judging\Listeners\Progress\QualifyingRecipients@handle',
            'AwardForce\Modules\Judging\Listeners\Progress\TopPickRecipients@handle',
            'AwardForce\Modules\Judging\Listeners\Progress\VipJudgingRecipients@handle',
            'AwardForce\Modules\Judging\Listeners\Progress\VotingRecipients@handle',
            'AwardForce\Modules\Contract\Listeners\ContractRecipients@handle',
            'AwardForce\Modules\Grants\Listeners\GrantRecipients@handle',
            'AwardForce\Modules\GrantReports\Listeners\GrantReportRecipients@handle',
        ],

        // Class events
        AccountWasCreated::class => [
            'AwardForce\Modules\Identity\Roles\Listeners\Account@whenAccountWasCreated',
            'AwardForce\Modules\Content\Blocks\Listeners\Account@whenAccountWasCreated',
            'AwardForce\Modules\Settings\Listeners\Account@whenAccountWasCreated',
            'AwardForce\Modules\Payments\Listeners\Account@whenAccountWasCreated',
            'AwardForce\Modules\Theme\Listeners\Account@whenAccountWasCreated',
            'AwardForce\Modules\Categories\Listeners\Account@whenAccountWasCreated',
            'AwardForce\Modules\Forms\Tabs\Events\Listeners\SeedDefault@whenAccountWasCreated',
            'AwardForce\Modules\Forms\Fields\Events\Listeners\SeedDefault@whenAccountWasCreated',
            'AwardForce\Modules\Grants\Listeners\Account@whenAccountWasCreated',
            'AwardForce\Modules\PaymentMethods\Listeners\Account@whenAccountWasCreated',
        ],
        AllocationPaymentWasCreated::class => [
            AllocationPaymentsDueListener::class.'@whenAllocationPaymentWasCreated',
        ],
        AllocationPaymentWasUpdated::class => [
            AllocationPaymentsDueListener::class.'@whenAllocationPaymentWasUpdated',
        ],
        AllocationPaymentStatusWasChanged::class => [
            AllocationPaymentsStatusListener::class.'@whenAllocationPaymentStatusWasChanged',
        ],
        AllocationPaymentWasDeleted::class => [
            AllocationPaymentsDueListener::class.'@whenAllocationPaymentWasDeleted',
        ],
        CartWasUpdated::class => [
            'AwardForce\Modules\Ecommerce\Cart\Listeners\CartListener@whenCartWasUpdated',
        ],
        CategoryDivisionsWereChanged::class => [
            ReassignEntryDivisionsListener::class.'@whenCategoryDivisionsWereChanged',
        ],
        GlobalCommunicationChannelWasAdded::class => [
            'AwardForce\Modules\Notifications\Events\SystemEventListener@whenGlobalCommunicationChannelWasAdded',
        ],
        UserWasInvited::class => [
            'AwardForce\Modules\Notifications\Events\SystemEventListener@whenUserWasInvited',
        ],
        EntryHealthWasFlagged::class => [
            EntryHealthListener::class.'@whenEntryHealthWasFlagged',
        ],
        EntryHealthWasCleared::class => [
            EntryHealthListener::class.'@whenEntryHealthWasCleared',
        ],
        EntryWasModerated::class => [
            SystemEventListener::class.'@whenEntryWasModerated',
        ],
        EntryWasSubmitted::class => [
            'AwardForce\Modules\Notifications\Events\SystemEventListener@whenEntryWasSubmitted',
            UpdateEntryDivisionListener::class.'@whenEntryWasSubmitted',
            ReviewFlowListener::class.'@whenEntryWasSubmitted',
        ],
        EntryResubmissionWasRequired::class => [
            SystemEventListener::class.'@whenEntryResubmissionWasRequired',
        ],
        EntryWasResubmitted::class => [
            'AwardForce\Modules\Notifications\Events\SystemEventListener@whenEntryWasResubmitted',
        ],
        EntryWasUpdated::class => [
            UpdateEntryDivisionListener::class.'@whenEntryWasUpdated',
            ThumbnailsListener::class.'@whenEntryWasUpdated',
        ],
        FieldWasCopied::class => [
            CopyTranslationsListener::class.'@whenModelWasCopied',
        ],
        GrantStatusWasChanged::class => [
            SystemEventListener::class.'@whenGrantStatusWasChanged',
        ],
        ItemWasAddedToCart::class => [
            CartListener::class.'@whenItemWasAddedToCart',
        ],
        ModelWasCopied::class => [
            CopyTranslationsListener::class.'@whenModelWasCopied',
        ],
        TagWasAdded::class => [
            SystemEventListener::class.'@whenEntryWasTagged',
        ],
        TagWasAddedAutomatically::class => [
            SystemEventListener::class.'@whenEntryWasTagged',
        ],
        ReviewStageWasDeleted::class => [
            ReviewFlowListener::class.'@whenReviewStageWasDeleted',
        ],
        ReviewStageWasRestored::class => [
            ReviewFlowListener::class.'@whenReviewStageWasRestored',
        ],
        ReviewTaskWasProceeded::class => [
            ReviewFlowListener::class.'@whenReviewTaskWasProceeded',
            SystemEventListener::class.'@whenReviewTaskWasProceeded',
            ForgetReviewTaskCountCache::class,
        ],
        ReviewTaskWasCreated::class => [
            SystemEventListener::class.'@whenReviewStageWasStarted',
            ForgetReviewTaskCountCache::class,
        ],
        ReviewTaskWasStopped::class => [
            ReviewFlowListener::class.'@whenReviewTaskWasStopped',
            SystemEventListener::class.'@whenReviewTaskWasStopped',
            ForgetReviewTaskCountCache::class,
        ],
        ReviewTaskWasReassigned::class => [
            SystemEventListener::class.'@whenReviewTaskWasReassigned',
        ],
        SendReviewTaskNotificationWasRequested::class => [
            SystemEventListener::class.'@whenSendReviewTaskNotificationWasRequested',
        ],
        ScoreSetConfigurationWasUpdated::class => [
            ScoreSetRecalculationListener::class.'@whenScoreSetConfigurationWasUpdated',
        ],
        ScoringCriterionWasCopied::class => [
            CopyTranslationsListener::class.'@whenModelWasCopied',
            'AwardForce\Modules\ScoringCriteria\Events\ScoringCriterionListener@whenScoringCriterionWasCopied',
        ],
        FormCopierWasStarted::class => [
            CategoriesCopierListener::class.'@whenFormCopierWasStarted',
            ScoreSetsCopierListener::class.'@whenFormCopierWasStarted',
            ScoringCriteriaCopierListener::class.'@whenFormCopierWasStarted',
            RoundsCopierListener::class.'@whenFormCopierWasStarted',
        ],
        SeasonCopierWasStarted::class => [
            AwardsSeasonCopierListener::class.'@whenCopierWasStarted',
            CategoriesCopierListener::class.'@whenSeasonCopierWasStarted',
            ChaptersSeasonCopierListener::class.'@whenCopierWasStarted',
            ScoreSetsCopierListener::class.'@whenSeasonCopierWasStarted',
            ScoringCriteriaCopierListener::class.'@whenSeasonCopierWasStarted',
            IntegrationsCopierListener::class.'@whenCopierWasStarted',
            NotificationsCopierListener::class.'@whenCopierWasStarted',
            ReviewFlowCopierListener::class.'@whenCopierWasStarted',
            RoundsCopierListener::class.'@whenSeasonCopierWasStarted',
        ],
        EmptySeasonWasCreated::class => [
            SeedDefault::class.'@handle',
            'AwardForce\Modules\Rounds\Listeners\Season@whenEmptySeasonWasCreated',
        ],
        RoleWasGranted::class => [
            SystemEventListener::class.'@whenRoleWasGranted',
        ],
        EntryWasIneligible::class => [
            SystemEventListener::class.'@whenEligibilityWasChanged',
        ],
        EntryWasEligible::class => [
            SystemEventListener::class.'@whenEligibilityWasChanged',
        ],
        GrantReportWasCreated::class => [
            GrantReportsListener::class.'@whenGrantReportWasCreated',
            SystemEventListener::class.'@whenGrantReportWasCreated',
        ],
        GrantReportDueDateWasUpdated::class => [
            GrantReportsListener::class.'@whenGrantReportDueDateWasUpdated',
        ],
        GrantReportWasSubmitted::class => [
            SystemEventListener::class.'@whenGrantReportWasSubmitted',
        ],
        AssignmentWasCreated::class => [
            SystemEventListener::class.'@whenAssignmentWasCreated',
        ],
        AssignmentWasCompletedByJudge::class => [
            SystemEventListener::class.'@whenAssignmentWasCompleted',
        ],
        EntryWasInvited::class => [
            SystemEventListener::class.'@whenFormInvitationWasCreated',
        ],
        CollaboratorWasInvited::class => [
            SystemEventListener::class.'@whenCollaboratorWasInvited',
        ],
        UserHasRegistered::class => [
            SystemEventListener::class.'@whenUserHasRegistered',
        ],
        UserWasAdded::class => [
            SystemEventListener::class.'@whenUserHasRegistered',
        ],
        DocumentWasCreated::class => [
            SystemEventListener::class.'@whenDocumentWasCreated',
        ],
        OrderWasDeleted::class => [
            CartListener::class.'@whenOrderWasDeleted',
        ],
        OrderWasPaid::class => [
            CartListener::class.'@whenOrderWasPaid',
        ],
        CategoryWasChanged::class => [
            EligibleTabsEventListener::class.'@whenCategoryWasChanged',
        ],
    ];

    public function boot()
    {
        // TODO: Reconsider scoped bindings
        // We need to register our processors as singletons, so that they can manage state between events
        $this->app->scoped(RoleGrantedProcessor::class);
        $this->app->scoped(TagsProcessor::class);
        $this->app->scoped(CategoryChapterProcessor::class);
        $this->app->scoped(ReviewTaskProcessor::class);

        parent::boot();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('events', function ($app) {
            return (new Dispatcher($app))->setQueueResolver(function () use ($app) {
                return $app->make('Illuminate\Contracts\Queue\Factory');
            });
        });
        parent::register();
    }

    public function shouldDiscoverEvents()
    {
        return true;
    }

    /**
     * Get the listener directories that should be used to discover events.
     *
     * @return array
     */
    protected function discoverEventsWithin()
    {
        return [
            $this->app->path('Modules/Forms/Fields/Events/Listeners'),
            $this->app->path('Modules/Forms/Forms/Events/Listeners'),
            $this->app->path('Modules/Forms/Tabs/Events/Listeners'),
            $this->app->path('Modules/Webhooks/Listeners'),
        ];
    }
}
