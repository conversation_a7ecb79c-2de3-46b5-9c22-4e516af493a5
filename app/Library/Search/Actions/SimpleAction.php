<?php

namespace AwardForce\Library\Search\Actions;

use Illuminate\Support\HtmlString;

abstract class SimpleAction extends ActionOverflowAction
{
    /** @var string */
    protected $routeResource;

    /** @var string */
    private $permissionResource;

    public function __construct(string $routeResource, string $permissionResource)
    {
        $this->routeResource = $routeResource;
        $this->permissionResource = $permissionResource;
    }

    abstract public function action(): string;

    protected function permissionAction(): string
    {
        return $this->action();
    }

    public function viewData($record): array
    {
        return [
            'resource' => $this->routeResource,
            'selected' => $record->id,
            'record' => $record,
            'params' => [
                'redirect' => request()->fullUrl(),
            ],
        ];
    }

    public function applies($record = null): bool
    {
        return \Consumer::can($this->permissionAction(), $this->permissionResource);
    }

    public function render($record): HtmlString
    {
        return new HtmlString(view('partials.list-actions.'.$this->action(), $this->viewData($record))->render());
    }
}
