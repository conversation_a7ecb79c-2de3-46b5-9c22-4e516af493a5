<?php

namespace AwardForce\Http\Controllers\Api\V2;

use AwardForce\Http\Requests\Api\V2\AllocationPayment\AddCommentToAllocationPayment;
use AwardForce\Http\Requests\Api\V2\AllocationPayment\CreateAllocationPaymentApiRequest;
use AwardForce\Http\Requests\Api\V2\AllocationPayment\UpdateAllocationPaymentApiRequest;
use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\AddCommentToAllocationPayment as AddCommentToAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\UpdateAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\AllocationPayments\Repositories\AllocationPaymentsRepository;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\PaymentMethods\Repositories\PaymentMethodsRepository;
use Dingo\Api\Http\Request;
use Platform\Bus\Commands\DeleteModelsCommand;
use Platform\Search\ApiVisibility;

class AllocationPaymentController extends ApiController
{
    public function create(CreateAllocationPaymentApiRequest $request)
    {
        /** @var Allocation $allocation */
        $allocation = app(AllocationRepository::class)->getBySlug($request->get('allocation'));

        $allocationPayment = $this->dispatchSync(
            new AddAllocationPaymentCommand(
                ...$this->getRequestInputs($request, $allocation)
            )
        );

        return $this->resourceResponse((string) $allocationPayment->slug, 'allocation_payment');
    }

    private function getRequestInputs(CreateAllocationPaymentApiRequest|UpdateAllocationPaymentApiRequest $request, Allocation $allocation): array
    {
        $paymentMethod = app(PaymentMethodsRepository::class)->getBySlug($request->get('payment_method'));

        return [
            $paymentMethod?->id,
            $request->get('status'),
            empty($reference = $request->get('reference')) ? null : $reference,
            (float) $request->get('amount'),
            $allocation->id,
            empty($dateDue = $request->get('date_due')) ? null : $dateDue,
            empty($datePaid = $request->get('date_paid')) ? null : $datePaid,
        ];
    }

    public function delete(Request $request)
    {
        $allocationPayment = $request->get('allocation_payment');

        $this->dispatch(new DeleteModelsCommand([$allocationPayment->id], $this->repository));

        return $this->response()->noContent();
    }

    public function update(UpdateAllocationPaymentApiRequest $request)
    {
        /** @var AllocationPayment $allocationPayment */
        $allocationPayment = $request->get('allocation_payment');

        $newAllocationPayment = $this->dispatchSync(
            new UpdateAllocationPaymentCommand(
                $allocationPayment,
                ...$this->getRequestInputs($request, $allocationPayment->allocation)
            )
        );

        return $this->resourceResponse((string) $newAllocationPayment->slug);
    }

    public function comment(AddCommentToAllocationPayment $request)
    {
        $comment = $this->dispatchSync(new AddCommentToAllocationPaymentCommand(
            current_account()->userId,
            $request->allocation_payment,
            $request->comment
        ));

        return ['slug' => (string) $comment->slug, 'comment' => $comment->comment, 'allocation-payment' => $request->allocation_payment];
    }

    public function columnator(array $parameters, ApiVisibility $apiVisibility)
    {
        return $this->columnatorFactory->forApi('allocation-payments.search', $apiVisibility, $parameters);
    }

    protected function repository()
    {
        return $this->repository = app(AllocationPaymentsRepository::class);
    }
}
