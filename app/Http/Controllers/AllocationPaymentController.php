<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\AllocationPayments\AddAllocationPaymentRequest;
use AwardForce\Http\Requests\AllocationPayments\SelectedAllocationPaymentRequest;
use AwardForce\Http\Requests\AllocationPayments\UndeleteAllocationPaymentsRequest;
use AwardForce\Http\Requests\AllocationPayments\UpdateAllocationPaymentRequest;
use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\DeleteAllocationPaymentsCommand;
use AwardForce\Modules\AllocationPayments\Commands\UndeleteAllocationPaymentsCommand;
use AwardForce\Modules\AllocationPayments\Commands\UpdateAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\AllocationPayments\Repositories\AllocationPaymentsRepository;
use AwardForce\Modules\AllocationPayments\Services\RequestTransformer;
use AwardForce\Modules\AllocationPayments\View\AllocationPayments;
use AwardForce\Modules\Comments\Services\AddComments;
use AwardForce\Modules\Comments\Services\AllocationPaymentComments;
use AwardForce\Modules\Comments\Tags\AllocationPaymentTag;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Platform\Http\Controller;

class AllocationPaymentController extends Controller
{
    use DispatchesJobs;

    public static string $resource = 'Payments';

    public function __construct(
        protected AllocationPaymentsRepository $allocationPayments,
        protected RequestTransformer $requestTransformer,
        protected AllocationRepository $allocationRepository
    ) {
    }

    public function index(Request $request, AllocationPayments $view)
    {
        return $this->respond('allocation-payment.index', $view);
    }

    public function create(AddAllocationPaymentRequest $request): JsonResponse|RedirectResponse
    {
        /** @var Allocation $allocation */
        $allocation = $this->allocationRepository->getById($request->get('allocation_id'));

        $allocationPayment = $this->dispatchSync(
            new AddAllocationPaymentCommand(...$this->requestTransformer->fromRequest($request, $allocation))
        );

        if ($request->wantsJson()) {
            return response()->json(['allocationPayment' => $allocationPayment]);
        }

        return redirect()->route('allocation-payment.index');
    }

    public function update(UpdateAllocationPaymentRequest $request): JsonResponse|RedirectResponse
    {
        $allocationPayment = $this->dispatchSync(
            new UpdateAllocationPaymentCommand(
                $request->allocationPayment,
                ...$this->requestTransformer->fromRequest($request, $request->allocation)
            )
        );

        if ($request->wantsJson()) {
            return response()->json(['allocationPayment' => app(AddComments::class)->toItem($allocationPayment, AllocationPaymentTag::class)]);
        }

        return redirect()->route('allocation-payment.index');
    }

    public function delete(SelectedAllocationPaymentRequest $request): JsonResponse|RedirectResponse
    {
        $total = count($selected = $request->get('selected', []));
        $deleted = $this->dispatchSync(new DeleteAllocationPaymentsCommand($selected));
        $isScheduled = $total - $deleted;

        if (! $request->wantsJson()) {
            return redirect()->back()
                ->withMessage(
                    trans_choice('allocation-payments.messages.allocation_payment_delete.count', $deleted).' '.
                    trans('allocation-payments.messages.allocation_payment_delete.success').' '.
                    ($isScheduled ?
                        trans_choice('allocation-payments.messages.allocation_payment_delete.count', $isScheduled).' '.
                        trans_choice('allocation-payments.messages.allocation_payment_delete.failure', $isScheduled) : '')
                );
        }

        if ($isScheduled) {
            throw ValidationException::withMessages([
                trans('allocation-payments.messages.delete_no_scheduled_allocation_payment'),
            ]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse
     */
    public function undelete(UndeleteAllocationPaymentsRequest $request)
    {
        $this->dispatch(new UndeleteAllocationPaymentsCommand($request->get('selected')));

        return redirect()->back()
            ->with(['message' => trans('allocation-payments.messages.undeleted'), 'type' => 'info']);
    }

    public function getComments(Request $request, AllocationPayment $allocationPayment): JsonResponse
    {
        $commentsData = (new AllocationPaymentComments())->forAllocationPayment($allocationPayment);

        $commentsData['tags'] = collect($commentsData['tags'])
            ->map(fn($tag) => (string) $tag)
            ->toArray();

        return response()->json($commentsData);
    }

    public function getAllocationPayments(Request $request): JsonResponse
    {
        return response()->json(app(AddComments::class)->toCollection(
            $this->allocationPayments->getByAllocation($request->fundAllocation),
            AllocationPaymentTag::class
        ));
    }
}
