<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Dashboard\DashboardRequest;
use AwardForce\Library\AIAgents\Contracts\TextGenerator;
use AwardForce\Library\AIAgents\Enums\Model;
use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use AwardForce\Modules\Dashboard\Reports\CategoryEntries;
use AwardForce\Modules\Dashboard\View\Dashboard;
use Platform\Http\Controller;

class DashboardController extends Controller
{
    public function index(DashboardRequest $request, Dashboard $view)
    {
        dd(app(TextGenerator::class)->prompt(new Prompt(Model::Claude<PERSON>onnet4, 'Tell me a short story about a brave knight.')));

        return $this->respond('dashboard.index', $view);
    }

    public function viewReport(DashboardRequest $request, CategoryEntries $view)
    {
        return $this->respond('dashboard.report', $view);
    }
}
