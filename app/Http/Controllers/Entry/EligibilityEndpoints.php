<?php

namespace AwardForce\Http\Controllers\Entry;

use AwardForce\Modules\Entries\Commands\UpdateCollaborationFlagsSubmittable;

trait EligibilityEndpoints
{
    /**
     * Render the eligibility view.
     *
     * @return mixed
     */
    public function eligibility($entry, $contentBlock)
    {

        $this->dispatch(new UpdateCollaborationFlagsSubmittable($entry));

        return $this->respond('entry.common.eligibility', compact('entry', 'contentBlock'));
    }
}
