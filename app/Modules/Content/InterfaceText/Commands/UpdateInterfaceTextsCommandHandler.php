<?php

namespace AwardForce\Modules\Content\InterfaceText\Commands;

use AwardForce\Modules\Content\InterfaceText\Contracts\InterfaceTextRepository;
use Platform\Localisation\Translation;

class UpdateInterfaceTextsCommandHandler
{
    /**
     * @var InterfaceTextRepository
     */
    protected $interfaceTextRepository;

    public function __construct(InterfaceTextRepository $interfaceTextRepository)
    {
        $this->interfaceTextRepository = $interfaceTextRepository;
    }

    /**
     * Update an interface text override
     */
    public function handle(UpdateInterfaceTextsCommand $command)
    {
        $existingInterfaceTexts = $this->interfaceTextRepository->getExistingOverridesByFields(array_keys($command->overrides))
            ->groupBy(['field', 'language']);

        foreach ($command->overrides as $field => $json) {
            $translations = is_array($json) ? $json : json_decode($json, true);

            foreach ($translations as $lang => $translationArray) {
                $translation = trim($translationArray['value'] ?? null);
                $default = trim($translationArray['default'] ?? null);
                $interfaceText = $existingInterfaceTexts[$field][$lang][0] ?? null;

                if ($interfaceText && (empty($translation) || $translation === $default)) {
                    $interfaceText->delete();
                } else {
                    $this->updateOrCreate($interfaceText, $field, $lang, safe_double_quotes(safe_string($translation)), $default);
                }
            }
        }
    }

    private function updateOrCreate(?Translation $interfaceText, string $field, string $lang, ?string $translation, ?string $default)
    {
        if (! $interfaceText) {
            if (! empty($translation) && $translation !== $default) {
                $interfaceText = $this->interfaceTextRepository->getNew([
                    'field' => $field,
                    'resource' => 'ui',
                    'language' => $lang,
                    'value' => $translation,
                ]);
                $this->interfaceTextRepository->save($interfaceText);
            }
        } elseif (empty($translation) || $translation === $default) {
            $interfaceText->delete();
        } else {
            $this->interfaceTextRepository->update($interfaceText, ['value' => $translation]);
        }
    }
}
