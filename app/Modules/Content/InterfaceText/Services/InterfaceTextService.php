<?php

namespace AwardForce\Modules\Content\InterfaceText\Services;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Cache\Cacher;
use AwardForce\Modules\Content\InterfaceText\Contracts\InterfaceTextRepository;
use AwardForce\Modules\Content\InterfaceText\HtmlInterfaceString;
use AwardForce\Modules\Content\Terms\Facades\Term;
use Facades\Platform\Strings\Output;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

class InterfaceTextService
{
    use Cacher;

    public function __construct(protected InterfaceTextRepository $interfaceTextRepository)
    {
    }

    /**
     * Returns true if all conditions to show "Edit this text" are fulfilled.
     *
     * @param  string|array|null  $trans
     */
    public function displayEditTextTooltip(string $key, $trans = null): bool
    {
        return once(fn() => $this->getDisplayTooltipsSetting()
            && $this->isOverridableKey($key)
            && ! $this->keyExcludedForTooltip($key)
            && (! (is_string($trans) && ! empty($trans)) || ! Str::contains($trans, '|')) // Exclude trans_choice
            && $this->isProgramManager());
    }

    /**
     * Returns true if given translation key is possible to override using interface text page.
     */
    public function isOverridableKey(string $key): bool
    {
        return in_array($key, $this->overridableFields(), true);
    }

    protected function getDisplayTooltipsSetting(): bool
    {
        return $this->requestCache('getDisplayTooltipsSetting', fn() => (bool) setting('display-interface-text-tooltips', false));
    }

    /**
     * Return an object representing the existing interface override with current and default translations.
     * If it doesn't exist yet, new object is created (but not persisted yet).
     *
     * For performance reasons it requires passing all existing overrides to avoid additional db requests.
     *
     * @return \stdClass
     */
    public function getOverride(string $field, $existingOverrides)
    {
        $new = new \stdClass();
        $new->field = $field;
        $new->descriptiveKey = $this->getDescriptiveKey($field);
        $new->translated = [];

        // Ensure value is always defined, use original as default input value
        foreach ($this->getLanguages() as $language) {
            $default = $this->getDefaultValue($field, $language);
            $new->translated[$language] = [
                'value' => safe_string($default), 'default' => safe_string($default),
            ];
        }

        // Every existing translation
        foreach (($existingOverrides[$field] ?? []) as $item) {
            $new->translated[$item->language]['value'] = safe_string($item->value);
        }

        return $new;
    }

    /**
     * Return a list of available overridables grouped by Entries / Categories / Home etc.
     *
     * @return array
     */
    public function overridablesGrouped()
    {
        return collect(Config::get('interface-text.overridable_keys'))
            ->filter(fn($array, $header) => $this->filterSections($array, $header))
            ->mapWithKeys(function ($array, $header) {
                $array = collect($array)->map(function ($translationKey) {
                    return [
                        'value' => $translationKey,
                        'text' => $this->getDefaultValue($translationKey),
                        'descriptiveKey' => $this->getDescriptiveKey($translationKey),
                    ];
                });

                return [trans('interface-text.interface-text-headers.'.Str::kebab($header)) => $array];
            })
            ->sortKeys()
            ->toArray();
    }

    /**
     * Return a list of available fields to be used in overrides.
     */
    public function overridableFields(): array
    {
        return once(static fn() => collect(Config::get('interface-text.overridable_keys'))
            ->flatten()
            ->all()
        );
    }

    protected function filterSections($array, $section): bool
    {
        $filters = config('interface-text.depends-on-features', []);
        if (! array_key_exists($section, $filters)) {
            return true;
        }

        return feature_enabled($filters[$section]);
    }

    /**
     * Returns the text equivalent for the given interfaceText key
     * Returning the tooltip for PM, a markdown if the text was override or the original trans
     *
     * @return mixed
     */
    public function text(string $lang, string $key, string|array $trans)
    {
        return once(function () use ($key, $trans) {
            // Add "Edit this text" balloon *after* the terms are applied, otherwise sentence_case() is broken
            if ($this->displayEditTextTooltip($key)) {
                return $this->wrapStringWithTooltip($key, $trans);
            }

            if ($this->isOverridableKey($key)) {
                return html_entity_decode(Output::html($trans));
            }

            return $trans;
        });
    }

    public function wrapStringWithTooltip(string $translationKey, ?string $text): HtmlInterfaceString
    {
        return once(function () use ($translationKey, $text) {
            $text = html_entity_decode(Output::html($text));

            if (starts_with($text, '<edit-interface-text')) {
                return new HtmlInterfaceString($text);
            }

            $payload = $this->tooltipPayload($translationKey, $text);

            return new HtmlInterfaceString(
                '<edit-interface-text '
                .'class="edit-interface-text" '
                .'payload="'.$payload.'"'
                .'>'.$text.'</edit-interface-text>'
            );
        });
    }

    public function tooltipPayload(string $translationKey, ?string $text = ''): string
    {
        return once(static function () use ($translationKey, $text) {
            $text = Output::html($text);
            $tooltipUrl = route('interface-text.index', ['selected' => $translationKey]);
            $tooltip = '<a href="'.$tooltipUrl.'">'.trans('interface-text.edit-this-text.title').'</a>';

            return base64_encode(json_encode(compact('tooltip', 'text')));
        });
    }

    public function stripPopoverTag($string): ?string
    {
        return strip_tags((string) $string);
    }

    protected function isProgramManager(): bool
    {
        return once(static fn() => Consumer::isProgramManager());
    }

    private function getLanguages(): array
    {
        return current_account()->supportedLanguages()->pluck('code')->toArray();
    }

    /**
     * Return the description when there is no translation defined
     *
     * @param  null  $lang
     */
    private function getDefaultValue(string $translationKey, $lang = null): string
    {
        $translatedValue = strip_tags(trans_no_overrides($translationKey, [], $lang));

        if ($translatedValue !== $translationKey) {
            return $translatedValue;
        }

        return mb_ucfirst(strip_tags(
            trans('interface-text.descriptions.'.str_replace('.', '_', $translationKey), [], $lang)
        ));
    }

    /**
     * Return a readable version of the translation key in English with term resolution
     *
     * @return string
     */
    private function getDescriptiveKey($key)
    {
        foreach ($keyParts = preg_split('/(\.|-|_)/', $key) as $i => $keyPart) {
            $isPlural = false;
            $keyTerm = Term::get($keyPart, 'en_GB');

            if (is_null($keyTerm) && $isPlural = Str::endsWith($keyPart, 's')) {
                $keyTerm = Term::get(Str::singular($keyPart), 'en_GB');
            }
            if (! is_null($keyTerm)) {
                $keyParts[$i] = ! $isPlural ? $keyTerm->singular : $keyTerm->plural;
            }
        }

        return mb_ucfirst(implode(' ', $keyParts));
    }

    /**
     * Returns true if given key should be excluded in tooltip display
     */
    protected function keyExcludedForTooltip(string $key): bool
    {
        return in_array($key, $this->keysExcludedForToolTip(), true);
    }

    /**
     * Returns all keys that should be excluded in tooltip display
     */
    protected function keysExcludedForToolTip(): array
    {
        return once(static fn() => config('interface-text.excluded_overridable_keys_for_tooltip', []));
    }
}
