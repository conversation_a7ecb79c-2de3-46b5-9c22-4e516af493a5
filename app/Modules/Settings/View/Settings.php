<?php

namespace AwardForce\Modules\Settings\View;

use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Accounts\Models\SupportedLanguages;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Enums\LocalIdShortcodeFormat;
use AwardForce\Modules\Settings\Services\UploadValidator;
use Illuminate\Http\Request;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Facades\Translator;

class Settings extends View
{
    use Uploadable;

    private $repository;
    private $accounts;
    private $files;
    private $request;
    private $validator;

    public function __construct(
        SettingRepository $repository,
        AccountRepository $accounts,
        FileRepository $files,
        Request $request,
        UploadValidator $validator
    ) {
        $this->repository = $repository;
        $this->accounts = $accounts;
        $this->files = $files;
        $this->request = $request;
        $this->validator = $validator;

        VueData::registerTranslations(['home.register.email', 'home.login.email', 'setting.form.email_required', 'setting.form.mobile_required', 'features.not_available', 'shared.learn_more']);
    }

    public function account()
    {
        return Translator::translate(current_account());
    }

    public function settings()
    {
        return $this->repository->getAllAsKeyValue();
    }

    public function languageIds()
    {
        return $this->supportedLanguages()
            ->pluck('code')
            ->toArray();
    }

    public function domain()
    {
        return explode('.', $this->account()->domains()->first()->domain)[0];
    }

    public function sharingImage()
    {
        return $this->files->getById(setting('sharing-image'));
    }

    public function uploader()
    {
        return $this->validator->setupUploader($this->setupUploader());
    }

    public function transcodeRequired()
    {
        return count($this->files->forBulkTranscodeJob($this->account()->id));
    }

    public function socialProviders()
    {
        $providers = $this->repository->getValueByKey('social-authentication');

        return array_filter(explode(',', $providers ?? ''));
    }

    public function defaultTexts(): array
    {
        $account = $this->account();
        $supportedLanguages = $account->supportedLanguages();
        $defaults = [];

        foreach ($supportedLanguages as $language) {
            $defaults['agreementToTerms'][$language->code] = $account->defaultAgreementToTerms($language->code);
            $defaults['consentToNotificationsAndBroadcasts'][$language->code] = $account->defaultConsentToNotificationsAndBroadcasts($language->code);
        }

        return $defaults;
    }

    public function agreementResource(): array
    {
        return [
            'translated' => $this->supportedLanguages()
                ->mapWithKeys(fn($language) => [
                    $language->code => [
                        'agreementToTerms' => $this->account()->agreementToTerms($language->code),
                    ],
                ])
                ->toArray(),
        ];
    }

    public function consentResource(): array
    {
        return [
            'translated' => $this->supportedLanguages()
                ->mapWithKeys(fn($language) => [
                    $language->code => [
                        'consentToNotificationsAndBroadcasts' => $this->account()->consentToNotificationsAndBroadcasts($language->code),
                    ],
                ])
                ->toArray(),
        ];
    }

    public function hasCustomAgreementText(): ?string
    {
        return $this->account()->customAgreementToTerms();
    }

    public function hasCustomConsentText(): ?string
    {
        return $this->account()->customConsentToNotificationsAndBroadcasts();
    }

    private function supportedLanguages(): SupportedLanguages
    {
        return $this->accounts->getSupportedLanguages(current_account());
    }

    private function localIdShortcodeFormat(): LocalIdShortcodeFormat
    {
        return LocalIdShortcodeFormat::tryFrom(
            $this->repository->getValueByKey(
                'local-id-shortcode-format',
                LocalIdShortcodeFormat::After->value
            )
        );
    }

    public function localIdShortcodeAfter(): bool
    {
        return $this->localIdShortcodeFormat() === LocalIdShortcodeFormat::After;
    }

    public function localIdShortcodeBefore(): bool
    {
        return ! $this->localIdShortcodeAfter;
    }
}
