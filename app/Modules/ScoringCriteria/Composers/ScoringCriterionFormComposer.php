<?php

namespace AwardForce\Modules\ScoringCriteria\Composers;

use AwardForce\Modules\Categories\Composers\CategoryComposable;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Casts\Json;
use Tectonic\LaravelLocalisation\Facades\Translator;

class ScoringCriterionFormComposer
{
    use CategoryComposable;

    private $categories;
    private $fields;
    private $scoreSets;
    private $seasons;
    private $forms;

    public function __construct(
        CategoryRepository $categories,
        FieldRepository $fields,
        ScoreSetRepository $scoreSets,
        SeasonRepository $seasons,
        FormRepository $forms
    ) {
        $this->categories = $categories;
        $this->scoreSets = $scoreSets;
        $this->fields = $fields;
        $this->seasons = $seasons;
        $this->forms = $forms;
    }

    public function compose(View $view)
    {
        $form = $this->form($view->criterion);

        $criterion = $view->criterion->toArray();
        $criterion['translated'] = $view->criterion->id
            ? $view->criterion->translated
            : (old_translatable('translated') ?: $this->newCriterionTranslations());
        $criterion['categories'] = $view->criterion->id ? $view->criterion->categories->pluck('id')->toArray() : old('categories') ?? [];
        $criterion['categoryOption'] = $view->criterion->id ? $view->criterion->categoryOption : old('categoryOption') ?? 'all';
        $criterion['scoringControl'] = $view->criterion->id ? $view->criterion->scoringControl : old('scoringControl');
        $criterion['fieldId'] = $view->criterion->id ? $view->criterion->fieldId : old('fieldId');
        $criterion['order'] = $view->criterion->id ? $view->criterion->order : old('order');
        $criterion['ignoreEmpty'] = $view->criterion->id ? $view->criterion->ignoreEmpty : old('ignoreEmpty');
        $criterion['scoresetId'] = $view->criterion->id ? $view->criterion->scoresetId : (int) old('scoresetId');
        $criterion['maximumScore'] = $view->criterion->id ? $view->criterion->maximumScore : old('maximumScore');
        $criterion['minimumScore'] = $view->criterion->id ? $view->criterion->minimumScore : old('minimumScore');
        $criterion['weight'] = $view->criterion->id ? $view->criterion->weight : old('weight');
        $criterion['scoringControl'] = $view->criterion->id ? $view->criterion->scoringControl : old('scoringControl');
        $criterion['scoringPrecision'] = $view->criterion->id ? $view->criterion->scoringPrecision : old('scoringPrecision');
        $criterion['scoringIncrement'] = $view->criterion->id ? $view->criterion->scoringIncrement : old('scoringIncrement');
        $criterion['hasScores'] = $view->criterion->hasScores();

        $view->translatedCriterion = $criterion;

        $seasonId = $this->getSeasonId($view->criterion);
        $scoreSets = $this->scoreSets->getForFormAndMode($form, ScoreSet::MODE_VIP);

        $view->form = $this->forms->isSeasonMultiform($seasonId) ? ['name' => $form->name, 'slug' => (string) $form->slug] : null;

        $view->scoreSets = Translator::translate($scoreSets);
        $view->categories = $this->formCategoriesForMultiselect($this->categories, $form);
        $view->categoryIds = $view->criterion->categories->pluck('id')->toArray();
        $view->fields = $this->fields($form);
        $view->translations = translations_for_vue(
            \Consumer::languageCode(),
            [
                'fields.preview.error',
                'scoring-criteria',
                'scoring-criteria.form.comment_scores_required.label',
                'scoring-criteria.form.comment_scores.label',
                'score-set.titles.commenting',
                'multiselect.select_all',
                'miscellaneous.optional',
                'miscellaneous.slider.current_value',
                'buttons',
                'form.selector.label',
                'fields.alerts.delete-option',
                'validation.required_generic',
            ]
        );
    }

    protected function newCriterionTranslations(): array
    {
        $translatableFields = app(ScoringCriterion::class)->getTranslatableFields();

        return current_account()->languages()
            ->pluck('code')
            ->mapWithKeys(fn(string $code) => [
                $code => collect($translatableFields)
                    ->mapWithKeys(fn(string $translatableField) => [$translatableField => ''])
                    ->merge(['recommendationLabel' => Json::encode(trans('scoring-criteria.default_recommendations.'.current_account()->vertical, locale: $code))])
                    ->all(),
            ])
            ->all();
    }

    protected function fields(Form $form): array
    {
        $fields = $this->fields->getVisibleForScoring($form);

        return Translator::shallow($fields)->map(function (Field $field) {
            return [
                'id' => $field->id,
                'title' => $field->title.($field->isRequired() ? '' : ' '.trans('scoring-criteria.form.field.optional')),
                'categories' => $field->categoryOption === 'all' ? true : $field->categories->pluck('id')->all(),
                'conditional' => ! is_null($field->conditionalFieldId),
                'content' => $field->isContent(),
            ];
        })
            ->sortBy('title', SORT_STRING | SORT_FLAG_CASE)
            ->values()
            ->all();
    }

    private function form(ScoringCriterion $criterion): Form
    {
        return translate($criterion->exists ? $criterion->scoreSet->form : FormSelector::selectedOrDefault());
    }

    /**
     * @return int
     */
    protected function getSeasonId(ScoringCriterion $criterion)
    {
        return $criterion->seasonId ?: SeasonFilter::getId();
    }
}
