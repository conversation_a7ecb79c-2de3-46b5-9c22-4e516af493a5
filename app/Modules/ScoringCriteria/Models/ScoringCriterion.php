<?php

namespace AwardForce\Modules\ScoringCriteria\Models;

use AwardForce\Library\Database\Eloquent\ConfigurationExporter;
use AwardForce\Library\Database\Eloquent\ExportsConfiguration;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Localisation\InjectTranslations;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Judging\Data\Score;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;
use AwardForce\Modules\ScoringCriteria\Events\ScoringCriterionAdded;
use AwardForce\Modules\ScoringCriteria\Events\ScoringCriterionWasCopied;
use AwardForce\Modules\Seasons\Models\Season;
use Eloquence\Behaviours\HasSlugs;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Platform\Database\Eloquent\Archivisation;
use Platform\Database\Eloquent\TranslatableModel;
use Tectonic\Localisation\Contracts\Translatable;
use Tectonic\Localisation\Translator\Translations;

/**
 * AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion
 *
 * @property int $id
 * @property string|null $slug
 * @property int $accountId
 * @property int|null $seasonId
 * @property int $scoresetId
 * @property int|null $fieldId
 * @property int $ignoreEmpty
 * @property bool $commentsAllowed
 * @property bool $commentsRequired
 * @property string $recommendations
 * @property array $recommendationsWithTranslations
 * @property string $maximumScore
 * @property string $minimumScore
 * @property string $weight
 * @property int|null $order
 * @property string|null $scoringControl
 * @property int $scoringPrecision
 * @property string $scoringIncrement
 * @property string $categoryOption
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property \Illuminate\Support\Carbon|null $deletedAt
 * @property-read \Baum\Extensions\Eloquent\Collection<int, Category> $categories
 * @property-read int|null $categoriesCount
 * @property-read Field|null $field
 * @property-read int $weightedMaximumScore
 * @property-read \Platform\Database\Eloquent\Collection<int, Score> $scores
 * @property-read int|null $scoresCount
 * @property-read ScoreSet|null $scoreset
 * @property-read Season|null $season
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Tectonic\LaravelLocalisation\Database\Translation> $translations
 * @property-read int|null $translationsCount
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion archived()
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion current()
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion forConfiguration()
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ScoringCriterion onlyTrashed()
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion query()
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereCategoryOption($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereFieldId($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereIgnoreEmpty($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereMaximumScore($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereMinimumScore($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereOrder($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereScoresetId($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereScoringControl($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereScoringIncrement($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereScoringPrecision($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|ScoringCriterion whereWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ScoringCriterion withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ScoringCriterion withoutTrashed()
 *
 * @mixin \Eloquent
 */
class ScoringCriterion extends Model implements ConfigurationExporter, Translatable
{
    use Archivisation;
    use ExportsConfiguration;
    use HasSlugs;
    use InjectTranslations;
    use SoftDeletes;
    use TranslatableModel;
    use Translations;

    public $table = 'scoring_criteria';

    const SCORING_CONTROL_SELECT = 'select';

    const SCORING_CONTROL_SLIDER = 'slider';

    const SCORING_CONTROL_INPUT = 'input';

    const SCORING_CONTROL_RECOMMENDATION = 'recommendation';

    const ARCHIVED_AT = 'score_sets.archived_at';

    /**
     * @var string
     */
    protected $copiedEvent = ScoringCriterionWasCopied::class;

    /**
     * @var array
     */
    protected $appends = ['weighted_maximum_score'];

    public function categories()
    {
        return $this->belongsToMany(Category::class)->withTrashed();
    }

    public function scoreset()
    {
        return $this->belongsTo(ScoreSet::class)->withTrashed();
    }

    public function scores()
    {
        return $this->hasMany(Score::class);
    }

    public function season()
    {
        return $this->belongsTo(Season::class)->withTrashed();
    }

    public function field()
    {
        return $this->belongsTo(Field::class)->withTrashed();
    }

    /**
     * The required translatable fields for this model.
     *
     * @return array
     */
    public function getTranslatableFields()
    {
        return ['title', 'label', 'helpText', 'hintText', 'shortcode', 'recommendationLabel'];
    }

    /**
     * The fields that need something appended to them when copied.
     *
     * @return array
     */
    public function getTranslatableCopyAppendFields()
    {
        return [
            'title' => 'scoring-criteria.actions.copy.append',
        ];
    }

    /**
     * Returns the weighted maximum score for the scoring criteria.
     *
     * @return int
     */
    public function getWeightedMaximumScoreAttribute()
    {
        return $this->weight * $this->maximumScore;
    }

    /**
     * Criteria aligned with content fields should not ignore empty fields.
     */
    public function setIgnoreEmptyAttribute($ignoreEmpty)
    {
        if ($field = $this->field) {
            $this->attributes['ignore_empty'] = $field->isContent() ? false : $ignoreEmpty;
        }
    }

    protected function recommendationsWithTranslations(): Attribute
    {
        return Attribute::make(
            get: fn() => collect(Json::decode($this->recommendations))
                ->map(fn(float $value, string $key) => new Recommendation($key, $value, $this->recommendationLabelTranslation($key)))
                ->all(),
        );
    }

    public function getRecommendationByValue(float $value): ?Recommendation
    {
        return collect($this->recommendationsWithTranslations)
            ->first(fn(Recommendation $recommendation) => $recommendation->value === $value);
    }

    private function recommendationLabelTranslation(string $key)
    {
        return array_get(Json::decode(
            array_get($this->translated, (consumer()?->user()?->preferredLanguage()->code() ?? default_language_code()).'.recommendationLabel')
        ), $key, $key);
    }

    /**
     * Returns the range of options allowed for this criterion.
     *
     * @return array
     */
    public function range()
    {
        $increment = $this->scoringControl == self::SCORING_CONTROL_INPUT ?
            1 / pow(10, $this->scoringPrecision) :
            $this->scoringIncrement;

        $options = range($this->minimumScore, $this->maximumScore, $increment);

        if (array_last($options) != $this->maximumScore) {
            $options[] = $this->maximumScore;
        }

        return $options;
    }

    public function hasScores(): bool
    {
        return $this->exists && $this->scores()->exists();
    }

    /**
     * Create a new instance.
     *
     * @param  int  $seasonId
     * @param  int  $scoresetId
     * @param  int  $fieldId
     * @param  int  $maximumScore
     * @param  int  $weight
     * @param  int  $order
     * @param  string  $scoringControl
     * @param  float  $scoringPrecision
     * @param  float  $scoringIncrement
     * @param  string  $categoryOption
     * @return ScoringCriterion
     */
    public static function add(
        $seasonId,
        $scoresetId,
        $fieldId,
        $maximumScore,
        $minimumScore,
        $weight,
        $order,
        $scoringControl,
        $scoringPrecision,
        $scoringIncrement,
        $recommendations,
        $categoryOption,
        bool $ignoreEmpty = false,
        bool $commentsAllowed = false,
        bool $commentsRequired = false
    ) {
        $criterion = self::make(
            $seasonId,
            $scoresetId,
            $fieldId,
            $maximumScore,
            $minimumScore,
            $weight,
            $order,
            $scoringControl,
            $scoringPrecision,
            $scoringIncrement,
            $recommendations,
            $categoryOption,
            $ignoreEmpty,
            $commentsAllowed,
            $commentsRequired
        );

        $criterion->raise(new ScoringCriterionAdded($criterion));

        return $criterion;
    }

    /**
     * @param  int  $seasonId
     * @param  int  $scoresetId
     * @param  int  $fieldId
     * @param  int  $maximumScore
     * @param  int  $weight
     * @param  int  $order
     * @param  string  $scoringControl
     * @param  float  $scoringPrecision
     * @param  float  $scoringIncrement
     * @param  string  $categoryOption
     * @return ScoringCriterion
     */
    public static function make(
        $seasonId,
        $scoresetId,
        $fieldId,
        $maximumScore,
        $minimumScore,
        $weight,
        $order,
        $scoringControl,
        $scoringPrecision,
        $scoringIncrement,
        $recommendations,
        $categoryOption,
        bool $ignoreEmpty = false,
        bool $commentsAllowed = false,
        bool $commentsRequired = false
    ) {
        $criterion = new ScoringCriterion;

        $criterion->seasonId = $seasonId;
        $criterion->scoresetId = $scoresetId;
        $criterion->fieldId = $fieldId;
        $criterion->maximumScore = $maximumScore;
        $criterion->minimumScore = $minimumScore;
        $criterion->weight = $weight;
        $criterion->order = $order;
        $criterion->scoringControl = $scoringControl;
        $criterion->scoringPrecision = $scoringPrecision;
        $criterion->scoringIncrement = $scoringIncrement;
        $criterion->recommendations = is_string($recommendations) ? implode("\r\n", explode_options($recommendations)) : $recommendations;
        $criterion->categoryOption = $categoryOption;
        $criterion->ignoreEmpty = $ignoreEmpty;
        $criterion->commentsAllowed = $commentsAllowed;
        $criterion->commentsRequired = $commentsRequired;
        $criterion->raise(new ScoringCriterionAdded($criterion));

        return $criterion;
    }

    /**
     * Determine if the model instance has been archived.
     *
     * @return bool
     */
    public function isArchived()
    {
        return ! is_null(Arr::get($this, 'scoreSet.archivedAt'));
    }

    public function scoringControlIsRecommendation(): bool
    {
        return $this->scoringControl === self::SCORING_CONTROL_RECOMMENDATION;
    }

    public function hasHintText()
    {
        return $this->hasTranslation('hintText', null, current_account()->defaultLanguage()->code);
    }

    public function maximumScoreFormatted()
    {
        $value = $this->maximumScore;

        if (intval($value) == $value) {
            return intval($value);
        }

        return $value;
    }

    public function minimumScoreFormatted()
    {
        $value = $this->minimumScore;

        if (intval($value) == $value) {
            return intval($value);
        }

        return $value;
    }

    public function allowsComments(): bool
    {
        if (isset($this->attributes['allow_comments'])) {
            return $this->attributes['allow_comments'];
        }

        return $this->commentsAllowed || $this->scoreSet->commentScores;
    }

    public function requiresComments(): bool
    {
        if (isset($this->attributes['require_comments'])) {
            return $this->attributes['require_comments'];
        }

        return $this->commentsRequired || $this->scoreSet->commentScoresRequired;
    }

    public function allowsInternalComments(): bool
    {
        if (! $this->scoreset) {
            return false;
        }

        return (new ScoreSetCollection([$this->scoreset]))->settingEnabled('internalComments');
    }

    public function configurationRelations(): array
    {
        return ['categories'];
    }
}
