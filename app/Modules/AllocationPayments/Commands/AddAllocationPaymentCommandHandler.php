<?php

namespace AwardForce\Modules\AllocationPayments\Commands;

use AwardForce\Library\Exceptions\UnsupportedCurrencyException;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use Platform\Events\EventDispatcher;

class AddAllocationPaymentCommandHandler
{
    use EventDispatcher;

    public function __construct(
        protected AllocationRepository $allocationRepository
    ) {
    }

    /**
     * @throws UnsupportedCurrencyException
     */
    public function handle(AddAllocationPaymentCommand $command)
    {
        /** @var Allocation $allocation */
        $allocation = $this->allocationRepository->getById($command->allocationId);

        $allocationPayment = AllocationPayment::add(
            $allocation->entry->seasonId,
            $command->paymentMethodId,
            $command->status,
            $command->reference,
            $command->getAmount(),
            $allocation->fund->currency->code(),
            $allocation->id,
            $allocation->entryId,
            $allocation->fundId,
            $command->dateDue,
            $command->datePaid,
        );

        $this->dispatch($allocationPayment->releaseEvents());

        $allocationPayment->load('allocation');

        return $allocationPayment;
    }
}
