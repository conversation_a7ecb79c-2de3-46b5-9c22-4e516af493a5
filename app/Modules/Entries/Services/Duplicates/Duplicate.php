<?php

namespace AwardForce\Modules\Entries\Services\Duplicates;

use AwardForce\Library\Database\Node\HasScopedNestedSet;
use AwardForce\Library\Database\Node\Node;
use AwardForce\Modules\Entries\Events\EntryWasConfirmedAsDuplicated;
use AwardForce\Modules\Entries\Events\EntryWasConfirmedAsPrimary;
use AwardForce\Modules\Entries\Models\Entry;
use Eloquence\Behaviours\HasCamelCasing;
use Illuminate\Support\Facades\DB;
use Platform\Events\Raiseable;

/**
 * @property int $id
 * @property int $accountId
 * @property int $seasonId
 * @property int $entryId
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property string|null $confirmedAt
 * @property bool $outOfDate
 * @property int|null $parentId
 * @property int|null $lft
 * @property int|null $rgt
 * @property int|null $depth
 * @property-read \Baum\Extensions\Eloquent\Collection<int, Duplicate> $children
 * @property-read int|null $childrenCount
 * @property-read Entry $entry
 * @property-read Duplicate|null $parent
 *
 * @method static \Baum\Extensions\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Duplicate dCallback(callable $callback)
 * @method static \Baum\Extensions\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Duplicate limitDepth($limit)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Duplicate newQuery()
 * @method static \Platform\Database\Eloquent\Builder|Duplicate preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate query()
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereConfirmedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereDepth($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereEntryId($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereLft($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereOutOfDate($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereParentId($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereRgt($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate withoutNode($node)
 * @method static \Platform\Database\Eloquent\Builder|Duplicate withoutRoot()
 * @method static \Platform\Database\Eloquent\Builder|Duplicate withoutSelf()
 *
 * @mixin \Eloquent
 */
class Duplicate extends Node
{
    use HasCamelCasing;
    use HasScopedNestedSet;
    use Raiseable;

    protected $rightColumnName = 'rgt';
    protected $leftColumnName = 'lft';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'duplicate_entries';

    /**
     * Columns which restrict what we consider our Nested Set list
     *
     * @var array
     */
    protected $scoped = ['season_id'];

    protected $casts = [
        'out_of_date' => 'boolean',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['entry_id', 'account_id', 'season_id', 'confirmed_at', 'out_of_date'];

    public function entry()
    {
        return $this->belongsTo(Entry::class)->withTrashed();
    }

    /**
     * Return the Duplicate model for the given entry.
     *
     * @return Duplicate
     */
    public static function getForEntry(Entry $entry)
    {
        return self::firstOrCreate([
            'account_id' => $entry->accountId,
            'season_id' => $entry->seasonId,
            'entry_id' => $entry->id,
        ]);
    }

    /**
     * The associated entry is primary if this node has no parent.
     *
     * @return bool
     */
    public function primary()
    {
        return is_null($this->parentId);
    }

    public function duplicateOf()
    {
        return $this->parent->entry ?? null;
    }

    public function confirmed()
    {
        return ! is_null($this->confirmedAt);
    }

    public function submitted()
    {
        return ! $this->entry->inProgress();
    }

    public function unconfirm()
    {
        $this->update(['confirmed_at' => null]);
    }

    public function markAsPrimary()
    {
        $this->makeRoot();
        $this->update(['out_of_date' => false]);
    }

    public function markAsDuplicate(Duplicate $duplicate)
    {
        $this->makeChildOf($duplicate);
        $this->update(['out_of_date' => false]);
    }

    public function markAsOutOfDate()
    {
        $this->update(['out_of_date' => true]);
    }

    public function confirmAndArchive(Entry $primary)
    {
        $this->update(['confirmed_at' => now()]);
        $this->entry->archive();

        $this->raiseAll([new EntryWasConfirmedAsDuplicated($this->entry), new EntryWasConfirmedAsPrimary($primary)]);
    }

    public function confirmNotDuplicate()
    {
        $this->makeRoot();
        $this->update(['confirmed_at' => now()]);
    }

    public function forcePrimary()
    {
        if (($previousRoot = $this->getRoot())->is($this)) {
            return;
        }

        DB::transaction(function () use ($previousRoot) {
            $this->makeRoot();
            $this->update(['confirmed_at' => now()]);

            $previousRoot->fresh()->makeChildOf($this);
            $previousRoot->update(['confirmed_at' => null]);
        });
    }

    public function markAsDuplicateAndArchive(Duplicate $primary)
    {
        $this->markAsDuplicate($primary);
        $this->confirmAndArchive($primary->entry);
    }

    public function unarchiveNotDuplicate()
    {
        $this->makeRoot();
        $this->update(['confirmed_at' => null]);
        $this->entry->unarchive();

        $this->raiseAll($this->entry->releaseEvents());
    }

    public function allAncestorEntryIds()
    {
        return $this->getAncestors()->pluck('entry_id');
    }

    public function primaryEntry()
    {
        $root = $this->getRoot();

        return $root ? $root->entry : null;
    }

    public function allDuplicateEntryIds()
    {
        $root = $this->getRoot();

        return $root ? $root->getDescendantsAndSelf()->pluck('entry_id') : [];
    }

    public function resourceLabel(): string
    {
        return $this->entry?->title ?? '';
    }
}
