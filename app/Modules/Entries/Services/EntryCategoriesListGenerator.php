<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Categories\Services\UploadValidator;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\FormFileMapper;
use Facades\Platform\Strings\Output;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Translator\Engine;

class EntryCategoriesListGenerator
{
    use Uploadable;

    /** @var CategoryRepository */
    protected $categories;

    /** @var ChapterRepository */
    protected $chapters;

    /** @var Engine */
    private $translator;

    /** @var FileRepository */
    private $files;

    /** @var UploadValidator */
    private $validator;

    /** @var FormFileMapper */
    private $entryFormFile;

    /** @var Collection */
    protected $categoriesList;

    /**
     * EntryCategoriesListGenerator constructor.
     */
    public function __construct(
        CategoryRepository $categories,
        ChapterRepository $chapters,
        Engine $translator,
        FileRepository $files,
        UploadValidator $validator,
        FormFileMapper $entryFormFile
    ) {
        $this->categories = $categories;
        $this->chapters = $chapters;
        $this->translator = $translator;
        $this->files = $files;
        $this->validator = $validator;
        $this->entryFormFile = $entryFormFile;
    }

    public function chapterCategoriesTree($chapterId, $seasonId, $active = true, $trashed = false, ?Form $form = null): Collection
    {
        $this->categoriesList = $this->translator->shallow($this->chapterCategoriesRaw($chapterId, $seasonId, $active, $trashed, $form)->keyBy('id')->map(
            function (Category $category) use ($chapterId, $seasonId, $active, $trashed, $form) {
                return $this->categories->getWithAllChildren($category, $chapterId, $seasonId, $active, $trashed, $form->id ?? 0);
            }
        )->collapse())->keyBy('id');

        $parents = $this->categoriesList->pluck('parent_id')->filter()->unique();

        $files = $this->files->getByResourceId(File::RESOURCE_CATEGORIES, $this->categoriesList->pluck('id')->toArray())->groupBy('resource_id');

        return $this->categoriesList->map(
            function (Category $category) use ($parents, $files) {
                return $this->mapCategory($category, $parents, $files);
            }
        )->values();
    }

    public function singleCategory(Category $category)
    {
        $category = $this->translator->shallow($category);

        $files = $this->files->getByResourceId(File::RESOURCE_CATEGORIES, $category->id)
            ->groupBy('resource_id');

        $parent = $this->categories->getById($category->parentId);

        return $this->mapCategory(
            $category,
            $parent ? collect([$parent->id]) : null,
            $files
        );
    }

    public function mapCategory(Category $category, ?Collection $parents = null, ?Collection $files = null)
    {
        $isLeaf = empty($parents) || ! $parents->contains($category->id);

        return [
            'active' => $category->active,
            'attachmentTypes' => $this->attachmentTypes($category),
            'chapterSlugs' => $this->chapterSlugs($category),
            'createdAt' => $category->createdAt ? (string) $category->createdAt : null,
            'description' => Output::html($category->description),
            'divisions' => $category->divisions,
            'entrantMaxEntries' => $category->entrantMaxEntries,
            'entryNameLabel' => $category->entryNameLabel,
            'files' => $files ? $this->mapFiles($category, $files) : [],
            'fillEntryName' => $category->fillEntryName,
            'hasChildren' => ! $isLeaf,
            'heading' => $category->image_heading,
            'id' => $category->id,
            'locked' => $category->locked,
            'maxImageWidth' => $category->maxImageWidth,
            'name' => lang($category, 'name'),
            'packingSlip' => $category->packingSlip,
            'parentId' => $category->parentId ?? null,
            'reassign' => $category->reassign,
            'slug' => (string) $category->slug,
            'translated' => $category->translated,
            'uploaderOptions' => obfuscate($this->uploaderOptions($category))->toHtml(),
            'promoted' => $category->promoted,
        ];
    }

    private function mapFiles(Category $category, Collection $files): array
    {
        if (! feature_enabled('sponsors')) {
            return [];
        }

        return $files->has($category->id) ?
            $files->get($category->id)->map(function ($file) {
                return $this->entryFormFile->mapFile($file);
            })->toArray() : [];
    }

    private function uploaderOptions(Category $category): ?array
    {
        $categoryId = $category->exists ? $category->id : null;

        return $this->validator
            ->setupUploader($this->setupUploader(), $categoryId)
            ->setTempPrefix($category->slug)
            ->setMultiSelect(true)
            ->options();
    }

    private function attachmentTypes(Category $category): ?array
    {
        return ! empty($category->attachmentTypes) ? array_map(function ($types) {
            return explode("\n", $types);
        }, $category->attachmentTypes) : null;
    }

    private function chapterSlugs(Category $category): array
    {
        return $category->chapters->pluck('slug')->map(function ($slug) {
            return (string) $slug;
        })->all();
    }

    private function chapterCategoriesRaw($chapterId, $seasonId, $active = true, $trashed = false, ?Form $form = null): Collection
    {
        if ($trashed) {
            $chapter = $this->chapters->getByIdsWithTrashed([$chapterId])->first();
        } else {
            $chapter = $this->chapters->getById($chapterId);
        }

        if (! $chapter || ($form && ! $this->formAppliesToChapter($chapter, $form))) {
            return new Collection;
        }

        $categories = $this->categories->getRootFromChapter($chapter, $seasonId, $active, $trashed, $form->id ?? 0);

        return $this->withChildrenInSeason($categories, $seasonId);
    }

    private function formAppliesToChapter(Chapter $chapter, Form $form): bool
    {
        return $form->appliesToAllChapters() || $chapter->forms()->whereFormId($form->id)->exists();
    }

    /**
     * @param  int  $seasonId
     * @return Collection
     */
    private function withChildrenInSeason(Collection $categories, $seasonId)
    {
        return $categories->filter(function ($category) use ($seasonId) {
            if ($category->children->count()) {
                return $category->children->pluck('seasonId')->contains($seasonId);
            }

            return true;
        });
    }
}
