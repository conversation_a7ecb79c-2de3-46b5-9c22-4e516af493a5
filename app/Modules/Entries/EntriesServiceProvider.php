<?php

namespace AwardForce\Modules\Entries;

use AwardForce\Library\Providers\Providable;
use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\AIAgents\Services\ContextResolver;
use AwardForce\Modules\Ecommerce\Cart\Events\CartWasProcessed;
use AwardForce\Modules\Ecommerce\Cart\Events\ItemWasRemovedFromCart;
use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasCreated;
use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Contracts\ContractRepository;
use AwardForce\Modules\Entries\Contracts\ContributorRepository;
use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Contracts\FieldPairingRepository;
use AwardForce\Modules\Entries\Contracts\LinkRepository;
use AwardForce\Modules\Entries\Events\AttachmentsOrderWasChanged;
use AwardForce\Modules\Entries\Events\AttachmentWasDeleted;
use AwardForce\Modules\Entries\Events\BulkDownloadHasCompleted;
use AwardForce\Modules\Entries\Events\EntryWasRestored;
use AwardForce\Modules\Entries\Events\EntryWasUpdated;
use AwardForce\Modules\Entries\Listeners\AddAttachmentToSubmittable;
use AwardForce\Modules\Entries\Listeners\AttachOrderToEntriesListener;
use AwardForce\Modules\Entries\Listeners\DeleteFileWhenAttachmentDeleted;
use AwardForce\Modules\Entries\Listeners\DuplicateEntries;
use AwardForce\Modules\Entries\Listeners\RecalculateValues;
use AwardForce\Modules\Entries\Listeners\ReviewFlow;
use AwardForce\Modules\Entries\Listeners\SendBulkDownloadNotification;
use AwardForce\Modules\Entries\Listeners\SubmitEntries;
use AwardForce\Modules\Entries\Listeners\UpdateReviewStatusTranslation;
use AwardForce\Modules\Entries\Repositories\EloquentAttachmentRepository;
use AwardForce\Modules\Entries\Repositories\EloquentContractRepository;
use AwardForce\Modules\Entries\Repositories\EloquentContributorRepository;
use AwardForce\Modules\Entries\Repositories\EloquentDuplicateRepository;
use AwardForce\Modules\Entries\Repositories\EloquentEntryRepository;
use AwardForce\Modules\Entries\Repositories\EloquentFieldPairingRepository;
use AwardForce\Modules\Entries\Repositories\EloquentLinkRepository;
use AwardForce\Modules\Entries\Services\AIAgent\EntryComposite;
use AwardForce\Modules\Entries\Services\AIAgent\EntryContext;
use AwardForce\Modules\Files\Events\FileUploaded;
use AwardForce\Modules\Files\Services\Thumbnails\ThumbnailsListener;
use AwardForce\Modules\ReviewFlow\Events\ReviewStageWasUpdated;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasProceeded;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasReset;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasStopped;

class EntriesServiceProvider extends ServiceProvider
{
    use Providable;

    /**
     * @var bool
     */
    public $defer = true;

    /**
     * Define the listeners for this module.
     *
     * @var array
     */
    protected $listeners = [
        CartWasProcessed::class => [
            SubmitEntries::class.'@whenCartWasProcessed',
        ],
        AttachmentWasDeleted::class => DeleteFileWhenAttachmentDeleted::class,
        EntryWasUpdated::class => DuplicateEntries::class.'@whenEntryWasUpdated',
        EntryWasRestored::class => [
            DuplicateEntries::class.'@whenEntryWasRestored',
            RecalculateValues::class,
        ],
        FileUploaded::class => [
            AddAttachmentToSubmittable::class.'@whenFileUploaded',
            ThumbnailsListener::class.'@whenFileUploaded',
        ],
        OrderWasCreated::class => AttachOrderToEntriesListener::class,
        BulkDownloadHasCompleted::class => SendBulkDownloadNotification::class,
        ReviewTaskWasProceeded::class => ReviewFlow::class.'@whenReviewTaskWasProceeded',
        ReviewTaskWasStopped::class => ReviewFlow::class.'@whenReviewTaskWasStopped',
        ReviewTaskWasReset::class => ReviewFlow::class.'@whenReviewTaskWasReset',
        ItemWasRemovedFromCart::class => SubmitEntries::class.'@whenItemWasRemovedFromCart',
        ReviewStageWasUpdated::class => UpdateReviewStatusTranslation::class.'@whenReviewStageWasUpdated',
        AttachmentsOrderWasChanged::class => ThumbnailsListener::class.'@whenAttachmentsOrderWasChanged',
    ];

    /**
     * The repository bindings for the Entries module.
     *
     * @var array
     */
    protected $repositories = [
        AttachmentRepository::class => EloquentAttachmentRepository::class,
        ContractRepository::class => EloquentContractRepository::class,
        ContributorRepository::class => EloquentContributorRepository::class,
        LinkRepository::class => EloquentLinkRepository::class,
        EntryRepository::class => EloquentEntryRepository::class,
        DuplicateRepository::class => EloquentDuplicateRepository::class,
        FieldPairingRepository::class => EloquentFieldPairingRepository::class,
    ];

    protected $files = [
        __DIR__.'/macros.php',
    ];

    public function register()
    {
        parent::register();

        $this->registerAIContexts();
    }

    private function registerAIContexts(): void
    {
        $this->app->tag(EntryComposite::class, ContextResolver::COMPOSITE);
        $this->app->tag(EntryContext::class, ContextResolver::CONTEXT);
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return array_keys($this->repositories);
    }

    /**
     * Get the events that trigger this service provider to register.
     *
     * @return array
     */
    public function when()
    {
        return array_keys($this->listeners);
    }
}
