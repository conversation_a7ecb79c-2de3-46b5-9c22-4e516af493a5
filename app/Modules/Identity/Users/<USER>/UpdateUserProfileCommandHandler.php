<?php

namespace AwardForce\Modules\Identity\Users\Commands;

use AwardForce\Modules\Accounts\Services\CurrentAccountService;
use AwardForce\Modules\Fields\Exceptions\CannotModifyFieldValue;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Services\UserUpdater;
use Platform\Events\EventDispatcher;
use Platform\Kessel\Exceptions\ResourceNotFound;
use Platform\Kessel\Exceptions\ValidationFailed;

class UpdateUserProfileCommandHandler
{
    use EventDispatcher;
    use UserUpdater;

    public function __construct(
        private CurrentAccountService $currentAccount,
        private UserRepository $users,
        private ValuesService $values
    ) {
    }

    /**
     * Handle the command.
     *
     * @throws CannotModifyFieldValue
     * @throws ResourceNotFound
     * @throws ValidationFailed
     */
    public function handle(UpdateUserProfileCommand $command): bool
    {
        $user = $command->user;

        $this->updateUser($user, [
            'firstName' => $command->firstName,
            'lastName' => $command->lastName,
            'email' => $command->email,
            'mobile' => $command->mobile,
            'password' => $command->password,
        ], true);

        $user->updateNotificationPreferences(
            $command->broadcastEmails,
            $command->notificationEmails,
            $command->notificationSms
        );

        if (setting('request-consent-to-cookies') && ! empty($command->cookies)) {
            $this->users->updateCookiePreferences($user, $command->cookies);
        }

        if (! empty($command->language)) {
            $languageChanged = $user->preferredLanguage()->code() !== $command->language;
            $user->setPreferredLanguage($this->currentAccount->getId(), $command->language);
        }

        if (! empty($colour = $command->colour)) {
            $user->currentMembership->setSetting('colour', $colour);
        }

        $this->values->syncValuesForObject($command->values, $user->currentMembership);

        $this->dispatch($this->uniqueUserAndMembershipEvents($user));

        return $languageChanged ?? false;
    }
}
