<?php

namespace AwardForce\Modules\Files\Models;

use AwardForce\Auth\Models\Account;
use AwardForce\Library\Filesystem\Metadata;
use AwardForce\Library\Filesystem\Storage;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Files\Values\Exposure;
use AwardForce\Modules\Files\Values\FocalLength;
use AwardForce\Modules\Files\Values\FStop;
use AwardForce\Modules\Files\Values\Iso;
use Exception;
use Illuminate\Support\Facades\Config;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class FileTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testVideoCheck(): void
    {
        $file1 = new File(['mime' => 'video/mp4']);
        $file2 = new File(['mime' => 'stuff']);

        $this->assertTrue($file1->isVideo());
        $this->assertFalse($file2->isVideo());
    }

    public function testTranscodedVideoCheck(): void
    {
        $account = new Account;
        Feature::shouldReceive('enabled')->with('transcoding')->andReturn(true);

        $file1 = new File(['mime' => 'video/mp4']);
        $file1->transcodingStatus = File::TRANSCODE_COMPLETED;
        $file1->setRelation('account', $account);

        $file2 = new File(['mime' => 'video/mp4']);
        $file2->transcodingStatus = File::TRANSCODE_WAITING;
        $file2->setRelation('account', $account);

        $this->assertTrue($file1->isTranscodedVideo());
        $this->assertFalse($file2->isTranscodedVideo());
    }

    public function testAudioCheck(): void
    {
        $file1 = new File(['mime' => 'audio/mp3']);
        $file2 = new File(['mime' => 'stuff']);

        $this->assertTrue($file1->isAudio());
        $this->assertFalse($file2->isAudio());
    }

    public function testImageCheck(): void
    {
        $file1 = new File(['mime' => 'image/jpeg']);
        $file2 = new File(['mime' => 'stuff']);

        $this->assertTrue($file1->isImage());
        $this->assertFalse($file2->isImage());
    }

    public function testLocation(): void
    {
        $file = new File(['file' => '/this/path/to/the/file.jpg']);

        $this->assertSame('this/path/to/the/', $file->location());
        $this->assertSame('this/path/to/the/file.jpg', $file->location('file.jpg'));
    }

    public function testTranscodingStatusException(): void
    {
        $this->expectException(Exception::class);

        $file = new File;
        $file->transcodingStatus = 'blargh';
    }

    public function testTranscodingStatusSuccess(): void
    {
        $file = new File;
        $file->transcodingStatus = 'WAITING';
        $file->transcodingStatus = 'progressing';
        $file->transcodingStatus = 'eRRor';
        $file->transcodingStatus = 'warning';
        $file->transcodingStatus = 'completed';

        $this->assertEquals('completed', $file->transcodingStatus);
    }

    public function testRetranscodeFeature(): void
    {
        $file = m::mock(File::class)->makePartial();
        $file->transcodingStatus = 'progressing';
        $file->transcodingOutputs = ['some stuff'];
        $file->shouldReceive('save')->once();
        $file->retranscode();

        $this->assertNull($file->transcodingStatus);
        $this->assertNull($file->transcodingOutputs);
    }

    public function testTranscodingOutputsFormat(): void
    {
        $file = new File;
        $file->transcodingStatus = 'error';
        $file->transcodingOutputs = ['errorCode' => '4000', 'errorMessage' => 'transcoding failed'];

        $this->assertSame(['4000: transcoding failed'], $file->transcodingErrors());
    }

    public function testIsFileField(): void
    {
        $file = new File;

        $file->resource = File::RESOURCE_ENTRIES;
        $this->assertFalse($file->isFileField());

        $file->resource = File::RESOURCE_FILE_FIELD;
        $this->assertFalse($file->isFileField());

        $file->resource = File::RESOURCE_FILE_FIELD.'-123';
        $this->assertTrue($file->isFileField());
    }

    public function testForAttachment(): void
    {
        $file = new File;

        $this->assertFalse($file->forAttachment());

        $file->resource = File::RESOURCE_ENTRIES;
        $this->assertFalse($file->forAttachment());

        $file->resource = File::RESOURCE_ATTACHMENTS;
        $this->assertTrue($file->forAttachment());
    }

    public function testIsCaption(): void
    {
        $file = new File;

        $this->assertFalse($file->isCaption());

        $file->resource = File::RESOURCE_ENTRIES;
        $this->assertFalse($file->forAttachment());

        $file->resource = File::RESOURCE_CAPTION;
        $this->assertTrue($file->isCaption());
    }

    public function testStripsLeadingSlash(): void
    {
        $file1 = new File(['file' => 'path/to/file1.jpg']);
        $file2 = new File(['file' => '/path/to/file2.jpg']);

        $this->assertEquals('path/to/file1.jpg', $file1->file);
        $this->assertEquals('path/to/file2.jpg', $file2->file);
    }

    public function testRefreshUsage(): void
    {
        /** @var File $file */
        $file = m::mock(File::class)->makePartial();
        $file->shouldReceive('save')->once();

        $file->file = 'files/a/b/c/d/e/1.txt';

        $this->app->instance(Storage::class, $storage = m::mock(Storage::class));
        $storage->shouldReceive('getDriver->listContents')
            ->with('files/a/b/c/d/e', true)
            ->andReturn([
                ['path' => 'files/a/b/c/d/e/1.txt', 'size' => 1100],
                ['path' => 'files/a/b/c/d/e/2.txt', 'size' => 1020],
                ['path' => 'files/a/b/c/d/e/3.txt', 'size' => 1003],
            ]);

        $file->refreshUsage();

        $this->assertEquals(3, $file->usageCount);
        $this->assertEquals(3123, $file->usageSize);
    }

    public function testGetsDurationFromTranscodingOutputs(): void
    {
        $file = new File;

        $output = new \stdClass;
        $output->id = 1;
        $output->key = '720p';
        $output->duration = 31;

        $file->transcodingOutputs = [$output];

        $this->assertEquals(31, $file->getDurationFromTranscodingOutputs());
    }

    public function testIsSingleUploadForField(): void
    {
        $singleUploadForField = $this->muffin(File::class, ['resource' => 'File field-123']);
        $singleUploadForAnotherField = $this->muffin(File::class, ['resource' => 'File field-1234']);
        $attachmentFile = $this->muffin(File::class, ['resource' => 'Entries']);
        $invalidFile = $this->muffin(File::class);

        $this->assertTrue($singleUploadForField->isSingleUploadForField(123));
        $this->assertFalse($singleUploadForAnotherField->isSingleUploadForField(123));
        $this->assertFalse($attachmentFile->isSingleUploadForField(123));
        $this->assertFalse($invalidFile->isSingleUploadForField(123));
    }

    public function testReplicateFile(): void
    {
        $file = File::create([
            'accountId' => current_account()->id,
            'token' => 'mGNsk3kpa8AwMAVP',
            'file' => 'files/E/E/8/y/1/m/t6I6sjzJ7b/file.mp4',
        ]);

        $copy = $file->replicate();
        $copy->save();

        $this->assertNotEquals($file->file, $copy->file);
        $this->assertStringEndsWith('file.mp4', $copy->file);

        $this->assertNotEquals($file->token, $copy->token);
        $this->assertEquals(16, strlen($copy->token));
    }

    public function testUnlinksResource(): void
    {
        $file = $this->muffin(File::class, [
            'resource' => File::RESOURCE_CATEGORIES,
            'resource_id' => ($category = $this->muffin(Category::class))->id,
        ]);

        $this->assertEquals($category->id, $file->resourceId);

        $file->unlinkResource();

        $this->assertNull($file->resourceId);
    }

    public function testImgixUrl(): void
    {
        $file = new File;
        $file->file = 'postcard.jpg';
        $file->mime = 'image/jpg';
        $file->status = 'ok';

        $imgixUrl = $file->imgixUrl();

        $this->assertStringContainsString('postcard.jpg?', $imgixUrl);
        $this->assertStringContainsString('auto', $imgixUrl);
    }

    public function testImgixThumbnailUrl(): void
    {
        $file = new File;
        $file->file = 'postcard.jpg';
        $file->mime = 'image/jpg';
        $file->status = 'ok';

        $imgixUrl = $file->imgixThumbnailUrl();

        $this->assertStringContainsString('postcard.jpg?', $imgixUrl);
        $this->assertStringContainsString('w=800', $imgixUrl);
    }

    public function testVideoHeightReturnsAccountHeightWhenLowerThanVideoHeightFile(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'FILE_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'heightInPx' => 720,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertEquals(600, $file->videoHeight(600));
    }

    public function testVideoHeightReturnsFileHeightWhenLowerThanAccountHeight(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'FILE_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'heightInPx' => 320,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertEquals(320, $file->videoHeight(600));
    }

    public function testVideoHeightReturnsDefaultHeightIfAccountHasNoHeight(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'FILE_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'heightInPx' => 720,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertEquals(450, $file->videoHeight(0));
    }

    public function testVideoHeightWithValidJsonWithoutHeight(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'FILE_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [],
                    ],
                ],
            ],
        ]);
        $this->assertEquals(450, $file->videoHeight(0));
    }

    public function testVideoHeightWithEmptyJson(): void
    {
        $file = $this->createFileWithTranscodingOutputs([]);
        $this->assertEquals(450, $file->videoHeight(0));
    }

    public function testVideoHeightWithoutTranscodingOutputs(): void
    {
        $file = $this->createFileWithTranscodingOutputs(null);
        $this->assertEquals(450, $file->videoHeight(0));
    }

    public function testVideoHeightReturnsDefaultWhenTranscodingOutputsDoesNotContainAType(): void
    {
        $file = $this->createFileWithTranscodingOutputs([[]]);
        $this->assertEquals(450, $file->videoHeight(0));
    }

    public function testVideoHeightReturnsDefaultWhenTranscodingOutputsIsNull(): void
    {
        $file = new File;
        $this->assertEquals(450, $file->videoHeight(0));
    }

    public function testVideoHeightReturnsDefaultValueWhenFallbackIsNull(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'FILE_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'heightInPx' => 720,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertEquals(450, $file->videoHeight(null));
    }

    public function testVideoAspectRatioReturnsCorrectAspectRatio(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'HLS_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'widthInPx' => 1920,
                            'heightInPx' => 1080,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertEquals('16:9', $file->videoAspectRatio());
    }

    public function testVideoAspectRatioHandlesSquareAspectRatio(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'HLS_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'widthInPx' => 1080,
                            'heightInPx' => 1080,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertEquals('1:1', $file->videoAspectRatio());
    }

    public function testVideoAspectRatioReturnsNullIfWidthOrHeightIsZero(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'HLS_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'widthInPx' => 1920,
                            'heightInPx' => 0,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertNull($file->videoAspectRatio());
    }

    public function testVideoAspectRatioReturnsNullWhenTranscodingOutputsIsEmpty(): void
    {
        $file = $this->createFileWithTranscodingOutputs([]);
        $this->assertNull($file->videoAspectRatio());
    }

    public function testVideoAspectRatioReturnsNullForInvalidJsonWithoutDimensions(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'HLS_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            // No width or height keys
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertNull($file->videoAspectRatio());
    }

    public function testVideoAspectRatioReturnsNullWhenNoHLSGroupIsPresent(): void
    {
        $file = $this->createFileWithTranscodingOutputs([
            [
                'type' => 'FILE_GROUP',
                'outputDetails' => [
                    [
                        'videoDetails' => [
                            'widthInPx' => 1920,
                            'heightInPx' => 1080,
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertNull($file->videoAspectRatio());
    }

    public function testVideoAspectRatioReturnsNullForEmptyTranscodingOutputs(): void
    {
        $file = $this->createFileWithTranscodingOutputs(null);
        $this->assertNull($file->videoAspectRatio());
    }

    public function testRetrieveOriginalNameWithoutInvisibleCharacters()
    {
        $file = new File;
        $file->original = '‎filename.‎jpeg';

        $this->assertEquals('filename.jpeg', $file->original);
    }

    public function testBelongsToSubmittable()
    {
        $file = new File;
        $file->foreignId = 1;
        $file->resource = File::RESOURCE_ENTRIES;
        $this->assertTrue($file->belongsToSubmittable());

        $file->resource = File::RESOURCE_CATEGORIES;
        $this->assertFalse($file->belongsToSubmittable());

        $file->resource = File::RESOURCE_FILE_FIELD;
        $this->assertTrue($file->belongsToSubmittable());

        $file->resource = File::RESOURCE_ATTACHMENTS;
        $this->assertTrue($file->belongsToSubmittable());

        $file->resource = File::RESOURCE_CONTRACTS;
        $this->assertFalse($file->belongsToSubmittable());
    }

    public function testItGetMetadataUrl(): void
    {
        $file = new File;
        $file->file = 'file.jpg';
        $file->status = 'ok';

        $this->assertEquals('https://mock.imgix.net/file.jpg?fm=json&ixlib=php-4.1.0', $file->getMetadataUrl());
    }

    public function testThumbnailWhenTranscodingIsDisabled()
    {
        Feature::shouldReceive('enabled')->with('transcoding')->andReturn(false);

        $video = new File;
        $video->file = 'file.mp4';
        $video->mime = 'video/mp4';

        $image = new File;
        $image->file = 'image.jpg';
        $image->mime = 'image/jpg';

        $this->assertEmpty($video->thumbnail());
        $this->assertSame('image.jpg', $image->thumbnail());
    }

    #[TestWith([1024 * 1024 * 20, 'image/jpeg', true])]
    #[TestWith([1024 * 1024 * 1024, 'image/jpeg', false])]
    #[TestWith([1024 * 1024 * 20, 'video/mp4', true])]
    #[TestWith([1024 * 1024 * 1024, 'video/mp4', true])]
    public function testIsThumbnailable(int $size, string $mime, bool $shouldBeThumbnailable)
    {
        Config::set('ui.images.max_file_size', 1024 * 1024 * 512); // 512MB

        $file = new File;
        $file->size = $size;
        $file->mime = $mime;
        $file->file = 'file.'.str_after($mime, '/');

        $this->assertEquals($shouldBeThumbnailable, $file->thumbnailable());
    }

    #[TestWith([true, true, true])]
    #[TestWith([true, false, false])]
    #[TestWith([false, true, false])]
    #[TestWith([false, false, false])]
    public function testFileTranscribableWhenStatusOkAndFeaturesAreConfigured(bool $transcodingEnabled, bool $autoCaptionEnabled, bool $isTranscribable): void
    {
        Feature::shouldReceive('enabled')->with('transcoding')->andReturn($transcodingEnabled);
        Feature::shouldReceive('enabled')->with('auto_caption')->andReturn($autoCaptionEnabled);

        $video = new File;
        $video->file = 'file.mp4';
        $video->mime = 'video/mp4';
        $video->status = File::STATUS_OK;

        $this->assertEquals($isTranscribable, $video->transcribable());
    }

    public function testFileShouldNotBeTranscribableIfItsStatusIsNotOk()
    {
        Feature::shouldReceive('enabled')->with('transcoding')->andReturn(true);
        Feature::shouldReceive('enabled')->with('auto_caption')->andReturn(true);

        $video = new File;
        $video->file = 'file.mp4';
        $video->mime = 'video/mp4';
        $video->status = File::STATUS_PENDING;

        $this->assertFalse($video->transcribable());
    }

    public function testItRetrievesMetadataFromCast()
    {
        $file = new File;

        $file->metadata = Metadata::fromServiceData([
            'IPTC' => ['ObjectName' => 'Test Image'],
            'PixelHeight' => 429,
            'PixelWidth' => 360,
            'TIFF' => ['Make' => 'SomeMake', 'Model' => 'Pentax Spotmatic'],
            'Exif' => ['FocalLength' => '50 mm', 'FNumber' => '2.8', 'ISOSpeedRatings' => 100, 'ExposureTime' => '1/125'],

        ]);
        $file->save();
        $file->refresh();

        $this->assertInstanceOf(Metadata::class, $file->metadata);
        $this->assertEquals('Test Image', $file->metadata->title);
        $this->assertEquals(429, $file->metadata->imageHeight);
        $this->assertEquals(360, $file->metadata->imageWidth);
        $this->assertEquals('SomeMake Pentax Spotmatic', (string) $file->metadata->camera);
        $this->assertEquals('360 x 429', (string) $file->metadata->size);
        $this->assertEquals((string) new Exposure('1/125'), (string) $file->metadata->exposure);
        $this->assertEquals((string) new FStop('2.8'), (string) $file->metadata->fStop);
        $this->assertEquals((string) new FocalLength('50 mm'), (string) $file->metadata->focalLength);
        $this->assertEquals((string) new Iso(['100']), (string) $file->metadata->iso);
    }

    private function createFileWithTranscodingOutputs(?array $transcodingOutputs): File
    {
        $file = new File;

        if ($transcodingOutputs) {
            $file->transcodingOutputs = $transcodingOutputs;
        }

        return $file;
    }
}
