<?php

namespace AwardForce\Modules\Forms\Forms\Database\Entities;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Database\Eloquent\ConfigurationExporter;
use AwardForce\Library\Database\Eloquent\ExportsConfiguration;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Enums\ScopeOption;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Models\HasFiles;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Repositories\SubmittableRepository;
use AwardForce\Modules\Forms\Forms\Events\FormWasCreated;
use AwardForce\Modules\Forms\Forms\Events\FormWasUpdated;
use AwardForce\Modules\Forms\Forms\Exceptions\InvalidFormType;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Traits\Seasonal;
use Carbon\Carbon;
use Eloquence\Behaviours\HasSlugs;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Platform\Database\Eloquent\Collection;
use Platform\Database\Eloquent\TranslatableModel;
use Tectonic\Localisation\Contracts\Translatable;
use Tectonic\Localisation\Translator\Translations;

/**
 * AwardForce\Modules\Forms\Forms\Database\Entities\Form
 *
 * @property int $id
 * @property int $accountId
 * @property int|null $contentblockId
 * @property int $seasonId
 * @property string|null $slug
 * @property string $type
 * @property int|null $order
 * @property string $chapterOption
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property \Illuminate\Support\Carbon|null $deletedAt
 * @property FormSettings $settings
 * @property-read Account|null $account
 * @property-read \Baum\Extensions\Eloquent\Collection<int, Category> $categories
 * @property-read int|null $categoriesCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Chapter> $chapters
 * @property-read int|null $chaptersCount
 * @property-read ContentBlock|null $contentBlock
 * @property-read \Platform\Database\Eloquent\Collection<int, ContentBlock> $contentBlocks
 * @property-read int|null $contentBlocksCount
 * @property-read \AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields<int, Field> $fields
 * @property-read int|null $fieldsCount
 * @property-read \AwardForce\Modules\Files\Models\FilesCollection<int, File> $files
 * @property-read int|null $filesCount
 * @property-read bool $isActive
 * @property-read Round|null $nextRound
 * @property-read \Platform\Database\Eloquent\Collection<int, Notification> $notifications
 * @property-read int|null $notificationsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Panel> $panels
 * @property-read int|null $panelsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Price> $prices
 * @property-read int|null $pricesCount
 * @property-read \Platform\Database\Eloquent\Collection<int, ReviewStage> $reviewStages
 * @property-read int|null $reviewStagesCount
 * @property-read \AwardForce\Modules\Rounds\Models\RoundCollection<int, Round> $rounds
 * @property-read int|null $roundsCount
 * @property-read \AwardForce\Modules\ScoreSets\Models\ScoreSetCollection<int, ScoreSet> $scoreSets
 * @property-read int|null $scoreSetsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, ScoringCriterion> $scoringCriteria
 * @property-read int|null $scoringCriteriaCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Search> $searches
 * @property-read int|null $searchesCount
 * @property-read Season|null $season
 * @property-read \Platform\Database\Eloquent\Collection<int, Tab> $tabs
 * @property-read int|null $tabsCount
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Tectonic\LaravelLocalisation\Database\Translation> $translations
 * @property-read int|null $translationsCount
 * @property-read Collection $roles
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Form forConfiguration()
 * @method static \Platform\Database\Eloquent\Builder|Form forSeason($seasonId)
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Form newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Form newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Form onlyTrashed()
 * @method static \Platform\Database\Eloquent\Builder|Form preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Form query()
 * @method static \Platform\Database\Eloquent\Builder|Form whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereAllowApiUpdates($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereApiUpdateable($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereBrowsable($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereChapterOption($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereCollaborative($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereContentblockId($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereCountdown($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereOrder($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereType($value)
 * @method static \Platform\Database\Eloquent\Builder|Form whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Form withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Form extends Model implements ConfigurationExporter, Translatable
{
    use ExportsConfiguration;
    use HasFiles;
    use HasSlugs;
    use Seasonal;
    use SoftDeletes;
    use TranslatableModel;
    use Translations;

    const CONTENT_BLOCK_ID_FIELD = 'contentblockId';

    /**
     * @var string
     */
    protected $table = 'forms';

    /**
     * @var string
     */
    const FORM_TYPE_ENTRY = 'entry';

    const FORM_TYPE_REPORT = 'report';

    const FORM_TYPE_EMPTY = 'empty';

    const CHAPTER_OPTION_ALL = 'all';

    const CHAPTER_OPTION_SELECT = 'select';

    /**
     * This is the list of all available form types users can choose from.
     *
     * NOTE: It should be updated if any new form types are introduced.
     */
    const AVAILABLE_TYPES = [
        self::FORM_TYPE_ENTRY,
        self::FORM_TYPE_REPORT,
    ];

    /**
     * This is the list of all form types that count towards a limit on how many forms can be created.
     *
     * NOTE: It should be updated if any new form types should count towards the limit.
     */
    const TYPES_WITH_LIMIT = [
        self::FORM_TYPE_ENTRY,
    ];

    protected $casts = [
        'settings' => FormSettings::class,
    ];

    public static function add(
        Season $season,
        string $type,
        FormSettings $settings,
        ?int $contentBlockId = null,
        int $order = 0,
    ): Form {
        $form = new Form;

        $form->accountId = $season->accountId;
        $form->seasonId = $season->id;
        $form->type = $type;
        $form->order = $order ?: null;
        $form->{self::CONTENT_BLOCK_ID_FIELD} = $contentBlockId;
        $form->settings = $settings;
        $form->createdAt = Carbon::now();
        $form->updatedAt = Carbon::now();

        $form->save();

        return $form;
    }

    /**
     * Checks if limit applies to the form type.
     */
    public static function limitApplies(string $type): bool
    {
        return in_array($type, self::TYPES_WITH_LIMIT);
    }

    /**
     * Checks if the form type is available for the current account plan.
     */
    public static function inAvailableTypes(?string $type): bool
    {
        if ($type === self::FORM_TYPE_REPORT && feature_disabled('grant_reports')) {
            return false;
        }

        return in_array($type, self::AVAILABLE_TYPES);
    }

    /**
     * @throws InvalidFormType
     */
    public function setTypeAttribute($value): void
    {
        if (! in_array($value, self::AVAILABLE_TYPES)) {
            throw new InvalidFormType;
        }

        $this->attributes['type'] = $value;
    }

    public function save(array $options = [])
    {
        if ($this->exists) {
            $this->raiseUnique(new FormWasUpdated($this));
        } else {
            $this->raiseUnique(new FormWasCreated($this));
        }

        return parent::save($options);
    }

    /**
     * Returns an array of field names that can be translated.
     *
     * @return array
     */
    public function getTranslatableFields()
    {
        return ['name', 'callToAction'];
    }

    public function getTranslatableCopyAppendFields()
    {
        return [
            'name' => 'miscellaneous.actions.copy.append',
        ];
    }

    /**
     * Form belongs to an Account.
     *
     * @return BelongsTo
     */
    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Forms must belong to one season.
     */
    public function season(): BelongsTo
    {
        return $this->belongsTo(Season::class);
    }

    public function reviewStages()
    {
        return $this->hasMany(ReviewStage::class);
    }

    public function scoreSets()
    {
        return $this->hasMany(ScoreSet::class);
    }

    public function scoringCriteria()
    {
        return $this->hasManyThrough(ScoringCriterion::class, ScoreSet::class, 'form_id', 'scoreset_id');
    }

    public function panels()
    {
        return $this->hasManyThrough(Panel::class, ScoreSet::class);
    }

    /*
     * TODO: REMOVE THE METHOD ONCE THE PLATFORM IS TESTED FOR ANY LEFTOVERS
     */
    public function contentBlocks()
    {
        return $this->hasMany(ContentBlock::class);
    }

    public function contentBlock(): BelongsTo
    {
        return $this->belongsTo(ContentBlock::class, 'contentblock_id');
    }

    public function entries()
    {
        return $this->hasMany(Entry::class);
    }

    public function grantReports()
    {
        return $this->hasMany(GrantReport::class);
    }

    /**
     * @throws InvalidFormType
     */
    public function submittable()
    {
        if ($this->isEntry()) {
            return $this->entries();
        }

        if ($this->isReport()) {
            return $this->grantReports();
        }

        throw new InvalidFormType;
    }

    public function submittableRepository(): SubmittableRepository
    {
        if ($this->isEntry()) {
            return app(EntryRepository::class);
        }

        if ($this->isReport()) {
            return app(GrantReportRepository::class);
        }

        throw new InvalidFormType;
    }

    public function findSubmittable(string $submittableSlug): ?Submittable
    {
        return $this->submittable()->firstWhere('slug', $submittableSlug);
    }

    public function categories()
    {
        return $this->hasMany(Category::class);
    }

    public function fields()
    {
        return $this->hasMany(Field::class);
    }

    public function tabs()
    {
        return $this->hasMany(Tab::class);
    }

    /**
     * Form belong to many Chapters
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function chapters()
    {
        return $this->belongsToMany(Chapter::class);
    }

    /**
     * Form has many Rounds
     *
     * @return \Illuminate\Database\Eloquent\Relations\hasMany
     */
    public function rounds()
    {
        return $this->hasMany(Round::class);
    }

    /**
     * Form belong to many Notifications
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function notifications()
    {
        return $this->belongsToMany(Notification::class);
    }

    public function prices()
    {
        return $this->belongsToMany(Price::class);
    }

    public function searches(): HasMany
    {
        return $this->hasMany(Search::class);
    }

    public function nextRound(): HasOne
    {
        return $this->hasOne(Round::class)
            ->select(['rounds.id', 'rounds.slug', 'rounds.form_id', 'rounds.starts_at', 'rounds.ends_at'])
            ->next();
    }

    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class);
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->rounds->active()->isNotEmpty();
    }

    public function edit(
        string $type,
        string $chapterOption,
        FormSettings $settings,
        int $order = 0,
        ?int $contentBlockId = null
    ) {
        $this->type = $type;
        $this->chapterOption = $chapterOption;
        $this->order = $order ?: null;
        $this->settings = $settings;
        $this->{self::CONTENT_BLOCK_ID_FIELD} = $contentBlockId;
        $this->save();

        $this->raise(new FormWasUpdated($this));
    }

    public function chapterIds(): array
    {
        $chapters = $this->chapterOption == 'select' ? $this->chapters : $this->season->chapters;

        return $chapters->pluck('id')->toArray();
    }

    public function getChapters()
    {
        return $this->chapterOption == Form::CHAPTER_OPTION_ALL ? $this->season->chapters : $this->chapters;
    }

    public function appliesToAllChapters(): bool
    {
        return $this->chapterOption == self::CHAPTER_OPTION_ALL;
    }

    public function appliesToSelectedChapters(): bool
    {
        return $this->chapterOption == self::CHAPTER_OPTION_SELECT;
    }

    public function appliesToChapter(Chapter $chapter): bool
    {
        return $this->appliesToAllChapters() || $chapter->forms()->whereFormId($this->id)->first() != null;
    }

    public function isReport(): bool
    {
        return $this->type === self::FORM_TYPE_REPORT;
    }

    public function isEntry(): bool
    {
        return $this->type === self::FORM_TYPE_ENTRY;
    }

    public function oneStepConfiguration()
    {
        return $this->isReport();
    }

    public function isTabTypeAllowed(string $type)
    {
        if ($this->isEntry()) {
            return true;
        }

        if ($this->isReport()) {
            return in_array($type, [Tab::TYPE_ATTACHMENTS, Tab::TYPE_CONTRIBUTORS, Tab::TYPE_FIELDS, Tab::TYPE_DETAILS]);
        }

        return false;
    }

    public function configurationRelations(): array
    {
        return ['rounds', 'chapters', 'files', 'roles'];
    }

    protected function fileResource(): string
    {
        return File::RESOURCE_FORM;
    }

    public function supportsRealTimeUpdates(): bool
    {
        return $this->supportsCollaboration() || $this->supportsApiUpdates();
    }

    public function supportsApiUpdates(): bool
    {
        return feature_enabled('api_submission_updates') && $this->settings->allowApiUpdates;
    }

    public function supportsCollaboration(): bool
    {
        return feature_enabled('collaboration') && $this->settings->collaborative;
    }

    public function roleEnabled(): bool
    {
        return $this->settings->roleVisibility === ScopeOption::All || $this->roles->intersect(Consumer::get()?->roles())->isNotEmpty();
    }
}
