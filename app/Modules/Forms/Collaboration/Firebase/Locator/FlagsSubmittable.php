<?php

namespace AwardForce\Modules\Forms\Collaboration\Firebase\Locator;

use AwardForce\Modules\Entries\Models\Entry;

final readonly class FlagsSubmittable extends BaseLocator
{
    public const COLLECTION = 'flags-submittable';

    public function __construct(private Entry $entry)
    {
    }

    public function documentParts(): array
    {
        return array_merge(
            (new Submittable($this->entry))->documentParts(),
        );
    }
}
