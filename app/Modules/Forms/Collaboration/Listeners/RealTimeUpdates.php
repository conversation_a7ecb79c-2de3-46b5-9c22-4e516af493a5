<?php

namespace AwardForce\Modules\Forms\Collaboration\Listeners;

use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Library\Encrypter\Strategies\Encrypter;
use AwardForce\Modules\Entries\Events\CategoryWasChanged;
use AwardForce\Modules\Entries\Events\ChapterWasChanged;
use AwardForce\Modules\Entries\Events\TitleWasChanged;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\ChapterCategory;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\RefereeEmail;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\RefereeName;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\Title;
use AwardForce\Modules\Forms\Fields\Events\FieldValueUpdated;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Referees\Events\EmailChanged;
use AwardForce\Modules\Referees\Events\NameChanged;

class RealTimeUpdates
{
    public function __construct(protected Database $database, protected Encrypter $encrypter)
    {
    }

    public function whenFieldValueWasUpdated(FieldValueUpdated $event): void
    {
        if (! $this->requiresRealTimeUpdate(($field = $event->field)->form)) {
            return;
        }

        $data = ['value' => [
            'sender' => null,
            'value' => $event->hasValues['values'][(string) $field->slug],
        ]];

        $this->database->set(
            $event->locator()->path('data.encrypted'),
            $this->encrypter->encrypt($data, $event->submittable()),
        );
    }

    public function whenCategoryOrChapterWasChanged(CategoryWasChanged|ChapterWasChanged $event): void
    {
        if (! $this->requiresRealTimeUpdate(($submittable = $event->entry)->form)) {
            return;
        }

        $data = ['value' => [
            'chapter' => $submittable->chapterId,
            'category' => $submittable->categoryId,
        ]];

        $this->database->set(
            (new ChapterCategory($submittable))->path('data.encrypted'),
            $this->encrypter->encrypt($data, $submittable),
        );
    }

    public function whenTitleWasChanged(TitleWasChanged $event): void
    {
        if (! $this->requiresRealTimeUpdate(($submittable = $event->entry)->form)) {
            return;
        }

        $data = ['value' => [
            'sender' => null,
            'value' => $submittable->title,
        ]];

        $this->database->set(
            (new Title($submittable))->path('data.encrypted'),
            $this->encrypter->encrypt($data, $submittable),
        );
    }

    public function whenRefereeNameChanged(NameChanged $event): void
    {
        if (! $this->requiresRealTimeUpdate(($referee = $event->referee)->submittable->form)) {
            return;
        }

        $data = ['value' => [
            'sender' => null,
            'value' => $referee->name,
        ]];

        $this->database->set(
            (new RefereeName($referee))->path('data.encrypted'),
            $this->encrypter->encrypt($data, $referee->submittable),
        );
    }

    public function whenRefereeEmailChanged(EmailChanged $event): void
    {
        if (! $this->requiresRealTimeUpdate(($referee = $event->referee)->submittable->form)) {
            return;
        }

        $data = ['value' => [
            'sender' => null,
            'value' => $referee->name,
        ]];

        $this->database->set(
            (new RefereeEmail($referee))->path('data.encrypted'),
            $this->encrypter->encrypt($data, $referee->submittable),
        );
    }

    private function requiresRealTimeUpdate(Form $form): bool
    {
        return is_api_consumer() && $form->supportsRealTimeUpdates();
    }
}
