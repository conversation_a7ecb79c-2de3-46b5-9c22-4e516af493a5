<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Translator\Engine;

class ConditionalFieldsListBuilder
{
    /**
     * @var \AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository
     */
    private $fieldRepository;

    /**
     * @var \Tectonic\LaravelLocalisation\Translator\Engine
     */
    private $translator;

    /**
     * ConditionalFieldsSelector constructor.
     */
    public function __construct(FieldRepository $fieldRepository, Engine $translator)
    {
        $this->fieldRepository = $fieldRepository;
        $this->translator = $translator;
    }

    public function getAvailableField($seasonId, $excludeField = null)
    {
        return $this->getAvailableFieldFromSet($this->getFields($seasonId, $excludeField));
    }

    public function getAvailableFormField($formId, $excludeField = null)
    {
        return $this->getAvailableFieldFromSet($this->getFieldsForForm($formId, $excludeField));
    }

    private function getAvailableFieldFromSet($allFields)
    {
        $list = $this->createFieldsCollection();

        $allFields->each(function ($field) use ($list) {
            $list[$field->resource]->put($field->id, ['title' => $field->title, 'type' => $field->type]);
        });

        // Return a collection of fields, grouped by resource and sorted alphabetically
        return $list->map(function ($group) {
            return $group->sort();
        });
    }

    /**
     * Create a new collection keyed by 'field.resource' type
     *
     * @return \Illuminate\Support\Collection
     */
    protected function createFieldsCollection()
    {
        return new Collection([
            Field::RESOURCE_ATTACHMENTS => new Collection,
            Field::RESOURCE_CONTRIBUTORS => new Collection,
            Field::RESOURCE_FORMS => new Collection,
            Field::RESOURCE_USERS => new Collection,
            Field::RESOURCE_REFEREES => new Collection,
        ]);
    }

    /**
     * Get all available fields (translated)
     *
     * @param  null|int  $exclude
     * @return mixed
     */
    protected function getFields($seasonId, $exclude)
    {
        return $this->translator->translate(
            $this->fieldRepository->getFieldsForConditionalList($seasonId, $exclude)
        );
    }

    /**
     * Get all available fields (translated) for form
     *
     * @param  null|int  $exclude
     * @return mixed
     */
    protected function getFieldsForForm($formId, $exclude)
    {
        return $this->translator->translate(
            $this->fieldRepository->getFormFieldsForConditionalList($formId, $exclude)
        );
    }
}
