<?php

namespace AwardForce\Modules\Forms\Fields\Values\Options;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Platform\Language\Language;
use Tests\IntegratedTestCase;
use Tests\MockConsumer;

final class OptionsTest extends IntegratedTestCase
{
    public function testMapsOptionsForList(): void
    {
        $field = new Field;
        $field->type = 'dropdownlist';
        $field->options = "op1\r\nop2\r\nop3";
        $field->addTranslation('en_GB', 'optionText', json_encode(['op1' => 'Option 1', 'op2' => 'Option 2', 'op3' => 'Option 3']));

        $mapped = $field->options->forList();

        $this->assertCount(3, $mapped);
        $this->assertEquals('op1', $mapped[0]['id']);
        $this->assertEquals('op2', $mapped[1]['id']);
        $this->assertEquals('op3', $mapped[2]['id']);
        $this->assertEquals('Option 1', $mapped[0]['text']);
        $this->assertEquals('Option 2', $mapped[1]['text']);
        $this->assertEquals('Option 3', $mapped[2]['text']);
    }

    public function testMapsOptionsForListWithFallback(): void
    {
        $field = new Field;
        $field->type = 'dropdownlist';
        $field->options = "op1\r\nop2\r\nop3";
        $field->addTranslation('en_GB', 'optionText', json_encode(['op1' => 'Option 1', 'op2' => 'Option 2', 'op3' => 'Option 3']));
        $field->addTranslation('el_GR', 'optionText', json_encode(['op1' => null, 'op2' => 'Greek 2']));

        $consumer = new MockConsumer;
        $consumer->language = new Language('el_GR');
        \Consumer::set($consumer);

        $mapped = $field->options->forList();

        $this->assertCount(3, $mapped);
        $this->assertEquals('op1', $mapped[0]['id']);
        $this->assertEquals('op2', $mapped[1]['id']);
        $this->assertEquals('op3', $mapped[2]['id']);
        $this->assertEquals('Option 1', $mapped[0]['text']);
        $this->assertEquals('Greek 2', $mapped[1]['text']);
        $this->assertEquals('Option 3', $mapped[2]['text']);
    }

    public function testCheckboxlistOptions(): void
    {
        $field = new Field;
        $field->type = 'dropdownlist';
        $field->options = "0\r\n1\r\n2\r\n3\r\n4\r\n5";
        $field->addTranslation('en_GB', 'optionText', json_encode(['zero', 'one', 'two', 'three', 'four', 'five']));

        $this->assertSame($field->options->getOptionText(), [0 => 'zero', 1 => 'one', 2 => 'two', 3 => 'three', 4 => 'four', 5 => 'five']);
    }

    public function testOptionTranslations(): void
    {
        $field = new Field;
        $field->type = 'dropdownlist';
        $field->options = "a\r\nb\r\nc";
        $field->addTranslation('en_GB', 'optionText', json_encode(['a' => 'OptionA', 'b' => 'OptionB', 'c' => 'OptionC']));

        $this->assertEquals('OptionA', $field->options->optionLang('a'));

        $this->assertEquals('NTA', $field->options->optionLang('d'));
    }

    public function testOptionTranslationsWithNumericOptionText(): void
    {
        $field = new Field;
        $field->type = 'dropdownlist';
        $field->options = "0\r\n1\r\n2";
        $field->addTranslation('en_GB', 'optionText', json_encode(['0', '1', '2']));

        $this->assertEquals('0', $field->options->optionLang(0));
    }

    public function testSelectedOptionText(): void
    {
        $field = new Field;
        $field->type = 'dropdownlist';
        $field->options = "o1\r\no2\r\nno3\r\nno4";
        $field->addTranslation('en_GB', 'optionText', json_encode(['o1' => 'one', 'o2' => 'two', 'no3' => 'three', 'no4' => 'four']));

        $value = 'o2';

        $this->assertEquals('two', $field->options->selectedOptionText($value));
        $this->assertEmpty($field->options->selectedOptionText(''));
    }

    public function testOptionsToString(): void
    {
        $this->setupUserWithRole('Entrant', true, 'el_GR');
        $field = $this->muffin(Field::class, [
            'type' => 'dropdownlist',
            'options' => "op1\r\nop2\r\nop3",
        ]);
        $field->addTranslation('en_GB', 'optionText', json_encode(['op1' => 'Option 1', 'op2' => 'Option 2', 'op3' => 'Option 3']));
        $field->addTranslation('el_GR', 'optionText', "Greek 1\r\nGreek 2\r\nGreek 3");

        $this->assertEquals('Greek 1, Greek 2, Greek 3', (string) $field->options);

        // With fallback
        $field = $this->muffin(Field::class, [
            'type' => 'dropdownlist',
            'options' => '{"op1":2,"op2":4,"op3":6}',
        ]);
        $field->addTranslation('en_GB', 'optionText', json_encode(['op1' => 'Option 1', 'op2' => 'Option 2', 'op3' => 'Option 3']));
        $field->addTranslation('el_GR', 'optionText', json_encode(['op1' => 'Greek 1', 'op3' => 'Greek 3']));

        $this->assertEquals('Greek 1, Option 2, Greek 3', (string) $field->options);
    }

    public function testMaxScore(): void
    {
        $radio = $this->muffin(Field::class, [
            'type' => 'radio',
            'options' => '{"one":1,"two":2,"three":3,"four":4}',
        ]);

        $dropdownlist = $this->muffin(Field::class, [
            'type' => 'dropdownlist',
            'options' => '{"one":1,"two":2,"three":3,"four":4}',
        ]);

        $checkboxlist = $this->muffin(Field::class, [
            'type' => 'checkboxlist',
            'options' => '{"one":1,"two":2,"three":3,"four":4}',
        ]);

        $checkbox = $this->muffin(Field::class, [
            'type' => 'checkbox',
            'options' => '{"1":17}',
        ]);

        $this->assertEquals(4, $radio->options->maxScore());
        $this->assertEquals(4, $dropdownlist->options->maxScore());
        $this->assertEquals(10, $checkboxlist->options->maxScore());
        $this->assertEquals(17, $checkbox->options->maxScore());
    }

    public function testOptionsWithEmptyOption(): void
    {
        $field = new Field;
        $field->type = 'dropdownlist';
        $field->options = "op1\r\nop2\r\nop3";
        $field->addTranslation('en_GB', 'optionText', json_encode(['op1' => 'Option 1', 'op2' => 'Option 2', 'op3' => 'Option 3']));

        $mapped = $field->options->forListWithEmptyOption();

        $this->assertCount(4, $mapped);
        $this->assertEquals('op1', $mapped[1]['id']);
        $this->assertEquals('op2', $mapped[2]['id']);
        $this->assertEquals('op3', $mapped[3]['id']);
        $this->assertEquals('Option 1', $mapped[1]['text']);
        $this->assertEquals('Option 2', $mapped[2]['text']);
        $this->assertEquals('Option 3', $mapped[3]['text']);
    }

    public function testOptionsWithoutScore(): void
    {
        $field = new Field();
        $field->type = 'checkboxlist';
        $field->options = '{"Option":87}';

        $this->assertCount(1, $options = $field->options->forListWithoutScore());
        $this->assertArrayNotHasKey('score', Arr::get($options, 0));
    }

    public function testSelectedOptionTextHtmlEntities(): void
    {
        $field = $this->muffin(Field::class, ['type' => 'dropdownlist', 'protection' => Field::PROTECTION_STANDARD]);
        $options = [
            'Test Option' => 1,
            'Test Option2' => 4,
        ];
        DB::table('fields')
            ->where('id', $field->id)
            ->update(['options' => json_encode($options)]);

        DB::table('translations')
            ->insert([
                'account_id' => $field->account_id,
                'resource' => 'Field',
                'language' => 'en_GB',
                'field' => 'optionText',
                'foreign_id' => $field->id,
                'value' => json_encode(['Test Option' => 'Test Option', 'Test Option2' => 'Test Option2']),
            ]);
        $field = translate($field->fresh());

        foreach ($field->options as $key => $option) {
            $this->assertStringContainsString(' ', $key);
            $this->assertStringContainsString(' ', $option->value);

            $this->assertEquals($option->value, str_replace('&nbsp;', ' ', $option->value));
        }
    }
}
