<?php

namespace AwardForce\Modules\Forms\Fields\Values\Options;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Support\Collection;

class Options extends Collection
{
    private Field $field;

    public function setField(Field $field): Options
    {
        $this->field = $field;

        return $this;
    }

    /**
     * Create an `Options` collection from the given field
     */
    public static function fromField(Field $field, array $options): Options
    {
        $instance = (new self())->setField($field);

        foreach ($options as $value => $score) {
            if (is_array($score) && isset($score['score'])) {
                $score = $score['score'];
            }
            $instance->put($value, new Option($value, is_null($score) ? 0.0 : $score, $instance->optionText($value)));
        }

        return $instance;
    }

    /**
     * Return the actual option string values
     *
     * @return string[]
     */
    public function keys()
    {
        return array_keys($this->items);
    }

    /**
     * Get the score for a given option
     *
     * @param  ?string  $option
     * @return mixed
     */
    public function score(?string $option)
    {
        return $this->get($option)?->score;
    }

    public function maxScore()
    {
        return $this->field->type === 'checkboxlist'
            ? $this->field->options->sum('score')
            : ($this->field->options->max('score') ?: 0);
    }

    public function __toString(): string
    {
        return implode(', ', $this->getOptionText());
    }

    /**
     * Return a list of options in a format usable for list generation
     *
     * @return array
     */
    public function forList()
    {
        return $this->mapOptions()
            ->values()
            ->all();
    }

    public function forListWithoutScore()
    {
        return $this->mapOptions()
            ->map(fn($option) => array_except($option, 'score'))
            ->values()
            ->all();
    }

    /**
     * Comma separated list of option text for the given option(s) and ?language
     *
     * @param  null  $lang
     */
    public function selectedOptionText($options, $lang = null): string
    {
        return implode(
            ', ',
            array_map(
                fn($option) => $this->valueForOption($option, $lang),
                is_array($options) ? $options : [$options]
            )
        );
    }

    /**
     * Get an option with case-insensitive search
     *
     *
     * @return string|null
     */
    public function getOption($option)
    {
        $index = array_search(
            mb_strtolower($option),
            array_map(fn($op) => mb_strtolower($op), $this->keys())
        );

        return $index !== false ? $this->keys()[$index] : null;
    }

    public function scores(): array
    {
        return $this->mapWithKeys(fn($value, $option) => [$option => $value->score])->toArray();
    }

    /**
     * Get the translated text for a given option and ?language
     */
    public function optionLang($option = null, $lang = null): string
    {
        return ! is_null($option) ? $this->valueForOption($option, $lang) : 'NTA';
    }

    public function getOptionText($lang = null): array
    {
        return $this->map(fn(Option $option) => $option->text($lang))
            ->toArray();
    }

    private function valueForOption($value, $lang = null)
    {
        if (is_string($value) && ! strlen($value = trim($value))) {
            return '';
        }
        // truth check for missing options
        $existing = $this->get(html_entity_decode($value));

        return $existing ? $existing->text($lang) : 'NTA';
    }

    private function optionText($option)
    {
        return collect($this->field->translated)
            ->mapWithKeys(fn($trans, $lang) => [$lang => $trans['optionText'][$option] ?? null])
            ->filter(fn($value) => ! is_null($value) && $value !== '')
            ->toArray();
    }

    /**
     * Return a list of options in a format usable for list generation
     *
     * @return array
     */
    public function forListWithEmptyOption()
    {
        return $this->mapOptions()
            ->prepend(['id' => '', 'text' => '', 'score' => ''])
            ->values()
            ->toArray();
    }

    private function mapOptions(): Collection
    {
        return $this->map(fn(Option $option) => [
            'id' => $option->value,
            'text' => $option->text(),
            'score' => $option->score,
        ]);
    }
}
