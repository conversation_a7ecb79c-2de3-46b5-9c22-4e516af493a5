<?php
/**
 * =======================================
 * ===== IMPORTANT - PLATFORM CONFIG =====
 * =======================================
 *
 * Please note, this config file is provided by platform!
 * Ensure all changes that apply to platform are made there.
 * Be careful publishing and merging updates from platform.
 */

return [
    // All toggleable features on Award Force
    'features' => [
        'api',
        'api_submission_updates',
        'audit',
        'auto_caption',
        'awards.certificates',
        'aws_lambda_bulk',
        'broadcasts',
        'collaboration',
        'ckeditor',
        'configuration_copy',
        'contracts',
        'custom_domain',
        'custom_exports',
        'custom_roles',
        'documents',
        'eligibility',
        'formula_field',
        'fund_management',
        'grants',
        'grant_reports',
        'image_optimisation',
        'invitation_only_forms',
        'judging_mode.gallery',
        'judging_mode.qualifying',
        'judging_mode.top_pick',
        'judging_mode.vip_judging',
        'judging_mode.voting',
        'manage_duplicates',
        'mobile_registration_sms',
        'multi_chapter',
        'multiform',
        'multilingual',
        'order_payments',
        'packing_slips',
        'pdfs',
        'plagiarism_detection',
        'resubmission',
        'review_flow',
        'salesforce',
        'saved_views',
        'saml',
        'sponsors',
        'theming',
        'transcoding',
        'user_fields',
    ],

    'plans' => [
        'starter' => [
            'features' => [
                'custom_exports',
                'custom_roles',
                'image_optimisation',
                'judging_mode.vip_judging',
                'packing_slips',
                'pdfs',
                'user_fields',
                'saved_views',
            ],
            'limits' => [
                'max_entries' => 200,
                'max_fields' => 50,
                'max_upload' => '10MB',
                'max_file_capacity' => '5GB',
            ],
        ],
        'plus' => [
            'features' => [
                'awards.certificates',
                'broadcasts',
                'custom_exports',
                'custom_roles',
                'image_optimisation',
                'judging_mode.gallery',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'multilingual',
                'packing_slips',
                'pdfs',
                'resubmission',
                'user_fields',
                'saved_views',
            ],
            'limits' => [
                'max_entries' => 500,
                'max_upload' => '10MB',
                'max_file_capacity' => '10GB',
            ],
        ],
        'professional' => [
            'features' => [
                'api',
                'awards.certificates',
                'broadcasts',
                'custom_domain',
                'custom_exports',
                'custom_roles',
                'eligibility',
                'formula_field',
                'image_optimisation',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'manage_duplicates',
                'multilingual',
                'order_payments',
                'packing_slips',
                'pdfs',
                'plagiarism_detection',
                'resubmission',
                'review_flow',
                'saved_views',
                'sponsors',
                'theming',
                'transcoding',
                'user_fields',
            ],
            'limits' => [
                'max_entries' => 10000,
                'max_video_viewing_minutes' => 100000,
            ],
        ],
        'enterprise' => [
            'features' => [
                'api',
                'api_submission_updates',
                'audit',
                'awards.certificates',
                'broadcasts',
                'collaboration',
                'custom_domain',
                'custom_exports',
                'custom_roles',
                'eligibility',
                'formula_field',
                'image_optimisation',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'manage_duplicates',
                'multi_chapter',
                'multilingual',
                'order_payments',
                'packing_slips',
                'pdfs',
                'plagiarism_detection',
                'resubmission',
                'review_flow',
                'saml',
                'salesforce',
                'saved_views',
                'sponsors',
                'theming',
                'transcoding',
                'user_fields',
            ],
        ],
        'premier' => [
            'features' => [
                'api',
                'api_submission_updates',
                'audit',
                'awards.certificates',
                'broadcasts',
                'collaboration',
                'custom_domain',
                'custom_exports',
                'custom_roles',
                'documents',
                'eligibility',
                'formula_field',
                'image_optimisation',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'manage_duplicates',
                'multilingual',
                'order_payments',
                'packing_slips',
                'pdfs',
                'plagiarism_detection',
                'resubmission',
                'review_flow',
                'saml',
                'saved_views',
                'sponsors',
                'theming',
                'transcoding',
                'user_fields',
            ],
            'limits' => [
                'max_entries' => 20000,
                'max_video_viewing_minutes' => 200000,
            ],
        ],
        'ggenterprise' => [
            'features' => [
                'api',
                'api_submission_updates',
                'audit',
                'awards.certificates',
                'broadcasts',
                'collaboration',
                'contracts',
                'custom_domain',
                'custom_exports',
                'custom_roles',
                'documents',
                'eligibility',
                'grants',
                'grant_reports',
                'formula_field',
                'fund_management',
                'image_optimisation',
                'invitation_only_forms',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'manage_duplicates',
                'multilingual',
                'multiform',
                'packing_slips',
                'pdfs',
                'plagiarism_detection',
                'resubmission',
                'review_flow',
                'saml',
                'saved_views',
                'sponsors',
                'theming',
                'transcoding',
                'user_fields',
            ],
            'limits' => [
                'max_forms' => 20,
                'max_entries' => 20000,
                'max_video_viewing_minutes' => 200000,
            ],
        ],
        'propublisher' => [
            'features' => [
                'api',
                'awards.certificates',
                'broadcasts',
                'custom_domain',
                'custom_exports',
                'custom_roles',
                'formula_field',
                'image_optimisation',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'manage_duplicates',
                'multilingual',
                'order_payments',
                'packing_slips',
                'pdfs',
                'plagiarism_detection',
                'resubmission',
                'saved_views',
                'sponsors',
                'theming',
                'user_fields',
            ],
            'limits' => [
                'max_entries' => 10000,
                'max_video_viewing_minutes' => 100000,
            ],
        ],
        'propartner' => [
            'features' => [
                'api',
                'awards.certificates',
                'broadcasts',
                'custom_domain',
                'custom_exports',
                'custom_roles',
                'formula_field',
                'image_optimisation',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'manage_duplicates',
                'multilingual',
                'order_payments',
                'packing_slips',
                'pdfs',
                'plagiarism_detection',
                'resubmission',
                'saved_views',
                'sponsors',
                'theming',
                'user_fields',
            ],
            'limits' => [
                'max_entries' => 10000,
                'max_video_viewing_minutes' => 100000,
            ],
        ],

        'intro' => [
            'features' => [
                'custom_exports',
                'custom_roles',
                'fund_management',
                'grants',
                'image_optimisation',
                'judging_mode.vip_judging',
                'multiform',
                'packing_slips',
                'pdfs',
                'resubmission',
                'user_fields',
                'saved_views',
            ],
            'limits' => [
                'max_entries' => 200,
                'max_fields' => 50,
                'max_upload' => '10MB',
                'max_file_capacity' => '5GB',
                'max_forms' => 1,
            ],
        ],
        'premium' => [
            'features' => [
                'api',
                'awards.certificates',
                'broadcasts',
                'custom_exports',
                'custom_roles',
                'contracts',
                'custom_domain',
                'documents',
                'eligibility',
                'formula_field',
                'fund_management',
                'grants',
                'grant_reports',
                'image_optimisation',
                'invitation_only_forms',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'manage_duplicates',
                'multiform',
                'multilingual',
                'packing_slips',
                'pdfs',
                'plagiarism_detection',
                'resubmission',
                'review_flow',
                'saved_views',
                'sponsors',
                'theming',
                'transcoding',
                'user_fields',
            ],
            'limits' => [
                'max_entries' => 10000,
                'max_video_viewing_minutes' => 100000,
                'max_forms' => 10,
            ],
        ],
        'growth' => [
            'features' => [
                'image_optimisation',
                'judging_mode.gallery',
                'judging_mode.qualifying',
                'judging_mode.top_pick',
                'judging_mode.vip_judging',
                'judging_mode.voting',
                'pdfs',
            ],
            'limits' => [
                'max_upload' => '10MB',
            ],
        ],
    ],

    /**
     * Map of permissions and the feature they require
     */
    'permissions' => [
        // permission => required_feature,
        'Audit' => 'audit',
        'ApiKeys' => 'api',
        'Grants' => 'grants',
        'Webhooks' => 'api',
    ],
];
